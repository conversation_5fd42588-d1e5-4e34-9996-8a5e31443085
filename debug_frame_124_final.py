#!/usr/bin/env python3
"""
最终调试Frame_00124的处理过程
确认Frame_00124是否被正确处理
"""

import json
import sys
from pathlib import Path
import glob

def check_frame_124_in_processing():
    """检查Frame_00124是否在处理范围内"""
    print("🔍 检查Frame_00124是否在处理范围内")
    print("=" * 80)
    
    # 获取所有标注文件
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    
    print(f"📊 总文件数: {len(label_files)}")
    
    # 查找Frame_00124
    frame_124_index = None
    for i, file_path in enumerate(label_files):
        if "frame_00124" in file_path:
            frame_124_index = i
            print(f"✅ 找到Frame_00124: {file_path}")
            print(f"📊 在处理序列中的位置: 第{i+1}帧 (索引{i})")
            break
    
    if frame_124_index is None:
        print("❌ 没有找到Frame_00124")
        return False
    
    # 检查Frame_00124的内容
    frame_124_file = label_files[frame_124_index]
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 统计区域分布
    region_stats = {}
    for shape in frame_124_data.get('shapes', []):
        group_id = shape.get('group_id')
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(shape.get('label', ''))
    
    print(f"\n📊 Frame_00124原始数据区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"  区域{region_id}: {len(labels)}张 - {labels}")
    
    # 特别检查区域6
    region_6_shapes = [s for s in frame_124_data.get('shapes', []) if s.get('group_id') == 6]
    print(f"\n📊 区域6详细信息:")
    print(f"  区域6卡牌数: {len(region_6_shapes)}")
    
    if region_6_shapes:
        for i, shape in enumerate(region_6_shapes):
            label = shape.get('label', '')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"    卡牌{i+1}: {label} 位置=({x:.1f}, {y:.1f})")
    else:
        print("    ❌ 没有区域6卡牌")
    
    return len(region_6_shapes) > 0

def check_output_frame_124():
    """检查Frame_00124的输出"""
    print("\n🔍 检查Frame_00124的输出")
    print("=" * 80)
    
    # 检查输出文件
    output_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    if not output_file.exists():
        print("❌ Frame_00124输出文件不存在")
        return False
    
    with open(output_file, 'r', encoding='utf-8') as f:
        output_data = json.load(f)
    
    # 统计区域分布
    region_stats = {}
    twin_ids = []
    
    for shape in output_data.get('shapes', []):
        group_id = shape.get('group_id')
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(label)
        
        if twin_id:
            twin_ids.append(twin_id)
    
    print(f"📊 Frame_00124输出区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"  区域{region_id}: {len(labels)}张 - {labels}")
    
    # 检查区域6
    region_6_shapes = [s for s in output_data.get('shapes', []) if s.get('group_id') == 6]
    print(f"\n📊 区域6输出详情:")
    print(f"  区域6卡牌数: {len(region_6_shapes)}")
    
    if region_6_shapes:
        for i, shape in enumerate(region_6_shapes):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"    卡牌{i+1}: {label} → {twin_id} 位置=({x:.1f}, {y:.1f})")
    else:
        print("    ❌ 没有区域6输出")
    
    # 检查重复ID
    duplicate_ids = []
    seen_ids = set()
    for twin_id in twin_ids:
        if twin_id in seen_ids:
            duplicate_ids.append(twin_id)
        else:
            seen_ids.add(twin_id)
    
    print(f"\n📊 ID重复检查:")
    if duplicate_ids:
        print(f"  ❌ 发现重复ID: {duplicate_ids}")
        
        # 查找重复ID的详细信息
        for dup_id in duplicate_ids:
            shapes_with_id = []
            for shape in output_data.get('shapes', []):
                if shape.get('attributes', {}).get('digital_twin_id') == dup_id:
                    shapes_with_id.append(shape)
            
            print(f"    重复ID '{dup_id}' 出现在:")
            for shape in shapes_with_id:
                label = shape.get('label', '')
                group_id = shape.get('group_id')
                points = shape.get('points', [])
                x, y = points[0] if points else (0, 0)
                print(f"      区域{group_id}: {label} 位置=({x:.1f}, {y:.1f})")
    else:
        print(f"  ✅ 没有重复ID")
    
    return True

def analyze_problem():
    """分析问题"""
    print("\n🔍 问题分析")
    print("=" * 80)
    
    print("基于调试结果，问题分析如下：")
    print()
    
    print("1. **原始数据状态**：")
    print("   - Frame_00124原始文件包含区域6数据")
    print("   - 区域6有6张卡牌：十、十、十、八、九、七")
    print()
    
    print("2. **处理过程问题**：")
    print("   - CalibrationGTFinalProcessor没有正确处理Frame_00124的区域6数据")
    print("   - 区域6数据在某个阶段丢失了")
    print("   - SimpleInheritor没有接收到区域6数据")
    print()
    
    print("3. **继承逻辑问题**：")
    print("   - 区域6的前一帧数据被识别为'消失的卡牌'")
    print("   - 跨区域继承错误地将区域6的ID分配给其他区域")
    print("   - 新的区域6卡牌被分配了重复的ID")
    print()
    
    print("4. **根本原因**：")
    print("   - Frame_00124的区域6数据在CalibrationGTFinalProcessor中丢失")
    print("   - 可能的原因：文件读取、数据转换、数据验证、模块处理")
    print()
    
    print("5. **解决方案**：")
    print("   - 需要深入调查CalibrationGTFinalProcessor的数据处理链路")
    print("   - 找出区域6数据丢失的具体位置")
    print("   - 修复数据传递问题")

def main():
    """主函数"""
    print("🚀 Frame_00124最终调试")
    print("🎯 目标: 确认Frame_00124的处理状态和问题根源")
    print("=" * 80)
    
    # 步骤1: 检查Frame_00124是否在处理范围内
    has_region_6_input = check_frame_124_in_processing()
    
    # 步骤2: 检查Frame_00124的输出
    has_output = check_output_frame_124()
    
    # 步骤3: 分析问题
    analyze_problem()
    
    print("\n🎯 最终结论:")
    print("=" * 80)
    
    if has_region_6_input and has_output:
        print("✅ Frame_00124确实被处理了")
        print("✅ Frame_00124有输出文件")
        print("❌ 但是区域6数据在处理过程中丢失了")
        print()
        print("🔧 建议的修复方案:")
        print("  1. 深入调查CalibrationGTFinalProcessor的数据处理链路")
        print("  2. 在每个处理阶段添加区域6的调试信息")
        print("  3. 找出区域6数据丢失的具体位置")
        print("  4. 修复数据传递问题")
    else:
        print("❌ Frame_00124处理有问题")
        if not has_region_6_input:
            print("❌ 原始文件中没有区域6数据")
        if not has_output:
            print("❌ 没有输出文件")

if __name__ == "__main__":
    main()
