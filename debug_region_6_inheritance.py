#!/usr/bin/env python3
"""
调试区域6的本区域继承逻辑
模拟区域6的继承过程，找出为什么本区域继承失败
"""

import json
import sys
from pathlib import Path

def simulate_region_6_inheritance():
    """模拟区域6的继承过程"""
    print("🔧 模拟区域6的本区域继承过程")
    print("=" * 80)
    
    # 读取Frame_00123和Frame_00124数据
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    frame_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    with open(frame_123_file, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 提取区域6的"八"卡牌
    def extract_region_6_ba_cards(data, frame_name):
        cards = []
        for shape in data.get('shapes', []):
            if shape.get('group_id') == 6 and '八' in shape.get('label', ''):
                cards.append({
                    'label': shape.get('label'),
                    'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                    'points': shape.get('points', []),
                    'shape': shape
                })
        print(f"📊 {frame_name}区域6的'八'卡牌: {len(cards)}张")
        for card in cards:
            x, y = card['points'][0] if card['points'] else (0, 0)
            print(f"  {card['label']} → {card['twin_id']} 位置=({x:.1f}, {y:.1f})")
        return cards
    
    previous_cards = extract_region_6_ba_cards(frame_123_data, "Frame_00123")
    current_cards = extract_region_6_ba_cards(frame_124_data, "Frame_00124")
    
    if not previous_cards or not current_cards:
        print("❌ 没有找到区域6的'八'卡牌")
        return False
    
    # 模拟区域6的排序规则：从下到上，从左到右
    def sort_cards_by_region_6_rule(cards):
        def get_position(card):
            points = card.get('points', [[0, 0]])
            if points and len(points) > 0:
                return points[0][0], points[0][1]  # x, y
            return 0, 0
        
        # 按Y坐标从大到小（从下到上），然后按X坐标从小到大（从左到右）
        sorted_cards = sorted(cards, key=lambda card: (-get_position(card)[1], get_position(card)[0]))
        return sorted_cards
    
    print("\n🔧 模拟区域6排序规则:")
    current_sorted = sort_cards_by_region_6_rule(current_cards)
    previous_sorted = sort_cards_by_region_6_rule(previous_cards)
    
    print("  当前帧排序后:")
    for i, card in enumerate(current_sorted):
        x, y = card['points'][0] if card['points'] else (0, 0)
        print(f"    {i+1}. {card['label']} 位置=({x:.1f}, {y:.1f})")
    
    print("  前一帧排序后:")
    for i, card in enumerate(previous_sorted):
        x, y = card['points'][0] if card['points'] else (0, 0)
        print(f"    {i+1}. {card['label']} → {card['twin_id']} 位置=({x:.1f}, {y:.1f})")
    
    # 模拟一对一匹配
    print("\n🔧 模拟一对一匹配:")
    if len(current_sorted) <= len(previous_sorted):
        print(f"  当前帧卡牌数({len(current_sorted)}) <= 前一帧卡牌数({len(previous_sorted)})")
        print("  使用空间排序匹配")
        
        matched_pairs = []
        for i in range(len(current_sorted)):
            current_card = current_sorted[i]
            previous_card = previous_sorted[i]
            matched_pairs.append((current_card, previous_card))
            
            print(f"  匹配{i+1}: {current_card['label']} ← {previous_card['twin_id']}")
        
        print(f"\n✅ 匹配成功: {len(matched_pairs)}对")
        return True
    else:
        print(f"  当前帧卡牌数({len(current_sorted)}) > 前一帧卡牌数({len(previous_sorted)})")
        print("  需要使用标签优先匹配")
        return False

def check_previous_frame_mapping():
    """检查前一帧映射是否正确构建"""
    print("\n🔍 检查前一帧映射构建")
    print("=" * 80)
    
    # 模拟前一帧映射的构建过程
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    
    with open(frame_123_file, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    # 模拟_update_previous_frame_mapping的逻辑
    previous_frame_mapping = {}
    
    for shape in frame_123_data.get('shapes', []):
        group_id = shape.get('group_id')
        label = shape.get('label', '')
        
        # 模拟_extract_original_label
        original_label = label  # 简化版本
        
        lookup_key = (group_id, original_label)
        if lookup_key not in previous_frame_mapping:
            previous_frame_mapping[lookup_key] = []
        previous_frame_mapping[lookup_key].append(shape)
    
    # 检查区域6的"八"映射
    lookup_key_6_ba = (6, '八')
    if lookup_key_6_ba in previous_frame_mapping:
        cards = previous_frame_mapping[lookup_key_6_ba]
        print(f"✅ 找到区域6的'八'映射: {len(cards)}张卡牌")
        for card in cards:
            twin_id = card.get('attributes', {}).get('digital_twin_id')
            print(f"  {card.get('label')} → {twin_id}")
    else:
        print(f"❌ 没有找到区域6的'八'映射")
        print(f"可用的映射键:")
        for key in previous_frame_mapping.keys():
            if key[0] == 6:
                print(f"  {key}")

def analyze_label_extraction():
    """分析标签提取逻辑"""
    print("\n🔍 分析标签提取逻辑")
    print("=" * 80)
    
    # 测试不同的标签提取方法
    test_labels = ['1八', '2八', '八']
    
    def extract_original_label_v1(label):
        """版本1：直接返回"""
        return label
    
    def extract_original_label_v2(label):
        """版本2：提取基础标签"""
        import re
        # 移除数字前缀
        match = re.match(r'^\d*(.+)$', label)
        if match:
            return match.group(1)
        return label
    
    print("📋 标签提取测试:")
    for label in test_labels:
        v1_result = extract_original_label_v1(label)
        v2_result = extract_original_label_v2(label)
        print(f"  '{label}' → v1:'{v1_result}' v2:'{v2_result}'")
    
    print("\n🚨 可能的问题:")
    print("  如果使用v1方法，'1八'和'2八'会被视为不同标签")
    print("  如果使用v2方法，'1八'和'2八'都会被视为'八'")
    print("  映射构建和查找必须使用相同的方法")

def main():
    """主函数"""
    print("🚀 区域6本区域继承调试")
    print("🎯 目标: 找出为什么区域6的本区域继承失败")
    print("=" * 80)
    
    # 步骤1: 模拟区域6的继承过程
    inheritance_success = simulate_region_6_inheritance()
    
    # 步骤2: 检查前一帧映射
    check_previous_frame_mapping()
    
    # 步骤3: 分析标签提取逻辑
    analyze_label_extraction()
    
    print("\n🎯 调试结论:")
    print("=" * 80)
    if inheritance_success:
        print("✅ 区域6的空间排序和匹配逻辑应该能够工作")
        print("🚨 问题可能出现在:")
        print("  1. 前一帧映射构建时的标签提取逻辑")
        print("  2. 查找时使用了不同的标签提取方法")
        print("  3. 映射键不匹配导致查找失败")
    else:
        print("❌ 区域6的匹配逻辑有问题")
        print("🚨 需要修复匹配算法")

if __name__ == "__main__":
    main()
