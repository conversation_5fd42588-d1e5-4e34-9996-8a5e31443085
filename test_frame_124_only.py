#!/usr/bin/env python3
"""
专门测试Frame_00124的处理
"""

import json
import os
import sys
sys.path.append('src')

from modules.phase2_integrator import Phase2Integrator

def test_frame_124():
    """测试Frame_00124的处理"""
    print("🔍 测试Frame_00124的处理")
    print("=" * 50)
    
    # 加载Frame_00123和Frame_00124的原始数据
    frame_123_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00123.json"
    frame_124_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00124.json"
    
    if not os.path.exists(frame_123_path) or not os.path.exists(frame_124_path):
        print("❌ 原始文件不存在")
        return
    
    with open(frame_123_path, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    with open(frame_124_path, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    print("Frame_00123原始数据中的'八'牌:")
    for shape in frame_123_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            print(f"  区域{shape.get('group_id')} ({shape.get('region_name')}): {shape.get('label')}")
    
    print("\nFrame_00124原始数据中的'八'牌:")
    for shape in frame_124_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            print(f"  区域{shape.get('group_id')} ({shape.get('region_name')}): {shape.get('label')}")
    
    # 创建Phase2Integrator实例
    integrator = Phase2Integrator()
    
    # 处理Frame_00123
    print("\n🔄 处理Frame_00123...")
    result_123 = integrator.process_frame(frame_123_data.get('shapes', []))

    if result_123.success:
        print("Frame_00123处理结果中的'八'牌:")
        for card in result_123.cards:
            if '八' in card.get('label', ''):
                print(f"  区域{card.get('group_id')} ({card.get('region_name')}): {card.get('label')} -> ID: {card.get('twin_id')}")
    else:
        print("Frame_00123处理失败")
        return

    # 处理Frame_00124
    print("\n🔄 处理Frame_00124...")
    result_124 = integrator.process_frame(frame_124_data.get('shapes', []))

    if result_124.success:
        print("Frame_00124处理结果中的'八'牌:")
        for card in result_124.cards:
            if '八' in card.get('label', ''):
                print(f"  区域{card.get('group_id')} ({card.get('region_name')}): {card.get('label')} -> ID: {card.get('twin_id')}")

        # 检查区域6的'1八'是否正确继承了'2八'的ID
        region_6_ba_cards = [card for card in result_124.cards if card.get('group_id') == 6 and '八' in card.get('label', '')]
        if region_6_ba_cards:
            card = region_6_ba_cards[0]
            expected_id = '2八'
            actual_id = card.get('twin_id')
            if actual_id == expected_id:
                print(f"\n✅ 修复成功！区域6的'{card.get('label')}' -> ID: {actual_id}")
            else:
                print(f"\n❌ 修复失败！区域6的'{card.get('label')}' -> ID: {actual_id}, 期望: {expected_id}")
        else:
            print("\n❌ 区域6没有'八'牌")
    else:
        print("Frame_00124处理失败")

if __name__ == "__main__":
    test_frame_124()
