import json

def analyze_frame(frame_data, frame_name):
    print(f'\n{frame_name} 分析:')
    
    # 按区域分组统计
    region_stats = {}
    for shape in frame_data['shapes']:
        group_id = shape['group_id']
        label = shape['label']
        digital_twin_id = shape['attributes'].get('digital_twin_id', 'N/A')
        
        if group_id not in region_stats:
            region_stats[group_id] = {}
        
        if label not in region_stats[group_id]:
            region_stats[group_id][label] = []
            
        region_stats[group_id][label].append(digital_twin_id)
    
    # 打印各区域统计
    for group_id in sorted(region_stats.keys()):
        print(f'  区域 {group_id}:')
        for label in sorted(region_stats[group_id].keys()):
            ids = region_stats[group_id][label]
            print(f'    {label}: {len(ids)} 张 - IDs: {ids}')

# 读取frame_00123数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00123.json', 'r', encoding='utf-8') as f:
    f123 = json.load(f)

# 读取frame_00124数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00124.json', 'r', encoding='utf-8') as f:
    f124 = json.load(f)

# 分析两个帧
analyze_frame(f123, 'Frame_00123')
analyze_frame(f124, 'Frame_00124')