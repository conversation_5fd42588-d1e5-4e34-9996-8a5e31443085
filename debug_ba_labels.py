#!/usr/bin/env python3
"""
调试Frame_00124中"八"的标签
检查是否有标签差异导致继承失败
"""

import json
from pathlib import Path

def debug_ba_labels():
    """调试"八"的标签"""
    print("🔍 调试Frame_00124中'八'的标签")
    print("=" * 80)
    
    # 检查Frame_00123
    frame_123_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00123.json")
    if frame_123_file.exists():
        with open(frame_123_file, 'r', encoding='utf-8') as f:
            frame_123_data = json.load(f)
        
        ba_shapes_123 = []
        for shape in frame_123_data.get('shapes', []):
            label = shape.get('label', '')
            if '八' in label:
                ba_shapes_123.append({
                    'label': label,
                    'group_id': shape.get('group_id'),
                    'points': shape.get('points', [])
                })
        
        print(f"📊 Frame_00123中的'八':")
        for i, shape in enumerate(ba_shapes_123):
            label = shape['label']
            group_id = shape['group_id']
            points = shape['points']
            x, y = points[0] if points else (0, 0)
            print(f"  八{i+1}: 标签='{label}', 区域={group_id}, 位置=({x:.1f}, {y:.1f})")
            # 检查标签的字节表示
            print(f"    标签字节: {label.encode('utf-8')}")
            print(f"    标签长度: {len(label)}")
    
    print()
    
    # 检查Frame_00124
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    if frame_124_file.exists():
        with open(frame_124_file, 'r', encoding='utf-8') as f:
            frame_124_data = json.load(f)
        
        ba_shapes_124 = []
        for shape in frame_124_data.get('shapes', []):
            label = shape.get('label', '')
            if '八' in label:
                ba_shapes_124.append({
                    'label': label,
                    'group_id': shape.get('group_id'),
                    'points': shape.get('points', [])
                })
        
        print(f"📊 Frame_00124中的'八':")
        for i, shape in enumerate(ba_shapes_124):
            label = shape['label']
            group_id = shape['group_id']
            points = shape['points']
            x, y = points[0] if points else (0, 0)
            print(f"  八{i+1}: 标签='{label}', 区域={group_id}, 位置=({x:.1f}, {y:.1f})")
            # 检查标签的字节表示
            print(f"    标签字节: {label.encode('utf-8')}")
            print(f"    标签长度: {len(label)}")
    
    print()
    
    # 检查输出结果
    output_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    if output_123_file.exists():
        with open(output_123_file, 'r', encoding='utf-8') as f:
            output_123_data = json.load(f)
        
        ba_output_123 = []
        for shape in output_123_data.get('shapes', []):
            label = shape.get('label', '')
            if '八' in label:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
                ba_output_123.append({
                    'label': label,
                    'group_id': shape.get('group_id'),
                    'twin_id': twin_id,
                    'points': shape.get('points', [])
                })
        
        print(f"📊 Frame_00123输出中的'八':")
        for i, shape in enumerate(ba_output_123):
            label = shape['label']
            group_id = shape['group_id']
            twin_id = shape['twin_id']
            points = shape['points']
            x, y = points[0] if points else (0, 0)
            print(f"  八{i+1}: 标签='{label}', 区域={group_id}, ID={twin_id}, 位置=({x:.1f}, {y:.1f})")
    
    print()
    
    output_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    if output_124_file.exists():
        with open(output_124_file, 'r', encoding='utf-8') as f:
            output_124_data = json.load(f)
        
        ba_output_124 = []
        for shape in output_124_data.get('shapes', []):
            label = shape.get('label', '')
            if '八' in label:
                twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
                ba_output_124.append({
                    'label': label,
                    'group_id': shape.get('group_id'),
                    'twin_id': twin_id,
                    'points': shape.get('points', [])
                })
        
        print(f"📊 Frame_00124输出中的'八':")
        for i, shape in enumerate(ba_output_124):
            label = shape['label']
            group_id = shape['group_id']
            twin_id = shape['twin_id']
            points = shape['points']
            x, y = points[0] if points else (0, 0)
            print(f"  八{i+1}: 标签='{label}', 区域={group_id}, ID={twin_id}, 位置=({x:.1f}, {y:.1f})")
    
    print()
    
    # 分析继承问题
    print("🔍 继承问题分析:")
    print("=" * 80)
    
    if ba_shapes_123 and ba_shapes_124:
        # 查找区域6的"八"
        region_6_ba_123 = [s for s in ba_shapes_123 if s['group_id'] == 6]
        region_6_ba_124 = [s for s in ba_shapes_124 if s['group_id'] == 6]
        
        print(f"Frame_00123区域6的'八': {len(region_6_ba_123)}张")
        for shape in region_6_ba_123:
            print(f"  标签='{shape['label']}', 位置=({shape['points'][0][0]:.1f}, {shape['points'][0][1]:.1f})")
        
        print(f"Frame_00124区域6的'八': {len(region_6_ba_124)}张")
        for shape in region_6_ba_124:
            print(f"  标签='{shape['label']}', 位置=({shape['points'][0][0]:.1f}, {shape['points'][0][1]:.1f})")
        
        # 检查标签是否完全相同
        if region_6_ba_123 and region_6_ba_124:
            label_123 = region_6_ba_123[0]['label']
            label_124 = region_6_ba_124[0]['label']
            
            print(f"\n标签比较:")
            print(f"  Frame_00123: '{label_123}' ({label_123.encode('utf-8')})")
            print(f"  Frame_00124: '{label_124}' ({label_124.encode('utf-8')})")
            print(f"  标签相同: {label_123 == label_124}")
            
            if label_123 != label_124:
                print(f"  ❌ 标签不同！这可能是继承失败的原因")
            else:
                print(f"  ✅ 标签相同，问题可能在其他地方")

if __name__ == "__main__":
    debug_ba_labels()
