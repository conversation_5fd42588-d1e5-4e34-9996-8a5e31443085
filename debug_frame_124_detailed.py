#!/usr/bin/env python3
"""
Frame_00124详细调试脚本
追踪完整的处理流程，找出继承错误的根本原因
"""

import json
import logging

def setup_debug_logging():
    """设置详细的调试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('frame_124_debug.log', mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def analyze_frame_124_inheritance():
    """分析Frame_00124的继承问题"""
    print("🔍 Frame_00124继承问题深度分析")
    print("=" * 80)
    
    # 1. 检查Frame_00123的输出结果
    print("\n1️⃣ 检查Frame_00123的输出结果")
    print("-" * 40)
    
    with open("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json", 'r', encoding='utf-8') as f:
        frame_123 = json.load(f)
    
    region_6_cards_123 = []
    for shape in frame_123.get('shapes', []):
        if shape.get('group_id') == 6:
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            region_6_cards_123.append({
                'label': shape.get('label'),
                'twin_id': twin_id,
                'region_name': shape.get('region_name')
            })
    
    print(f"Frame_00123区域6卡牌数量: {len(region_6_cards_123)}")
    for card in region_6_cards_123:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 特别关注'八'牌
    ba_cards_123 = [card for card in region_6_cards_123 if '八' in card['label']]
    print(f"\nFrame_00123区域6的'八'牌: {len(ba_cards_123)}张")
    for card in ba_cards_123:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 2. 检查Frame_00124的输出结果
    print("\n2️⃣ 检查Frame_00124的输出结果")
    print("-" * 40)
    
    with open("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json", 'r', encoding='utf-8') as f:
        frame_124 = json.load(f)
    
    region_6_cards_124 = []
    for shape in frame_124.get('shapes', []):
        if shape.get('group_id') == 6:
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            region_6_cards_124.append({
                'label': shape.get('label'),
                'twin_id': twin_id,
                'region_name': shape.get('region_name')
            })
    
    print(f"Frame_00124区域6卡牌数量: {len(region_6_cards_124)}")
    for card in region_6_cards_124:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 特别关注'八'牌
    ba_cards_124 = [card for card in region_6_cards_124 if '八' in card['label']]
    print(f"\nFrame_00124区域6的'八'牌: {len(ba_cards_124)}张")
    for card in ba_cards_124:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 3. 分析继承关系
    print("\n3️⃣ 分析继承关系")
    print("-" * 40)
    
    if ba_cards_123 and ba_cards_124:
        prev_card = ba_cards_123[0]  # Frame_00123的'八'牌
        curr_card = ba_cards_124[0]  # Frame_00124的'八'牌
        
        print(f"前一帧: {prev_card['label']} -> ID: {prev_card['twin_id']}")
        print(f"当前帧: {curr_card['label']} -> ID: {curr_card['twin_id']}")
        
        if curr_card['twin_id'] == prev_card['twin_id']:
            print("✅ 继承正确：ID保持一致")
        else:
            print("❌ 继承错误：ID不一致")
            print(f"   期望ID: {prev_card['twin_id']}")
            print(f"   实际ID: {curr_card['twin_id']}")
    
    # 4. 检查是否有区域1的'1八'
    print("\n4️⃣ 检查区域1的'八'牌")
    print("-" * 40)
    
    region_1_ba_cards_123 = []
    region_1_ba_cards_124 = []
    
    for shape in frame_123.get('shapes', []):
        if shape.get('group_id') == 1 and '八' in shape.get('label', ''):
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            region_1_ba_cards_123.append({
                'label': shape.get('label'),
                'twin_id': twin_id
            })
    
    for shape in frame_124.get('shapes', []):
        if shape.get('group_id') == 1 and '八' in shape.get('label', ''):
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            region_1_ba_cards_124.append({
                'label': shape.get('label'),
                'twin_id': twin_id
            })
    
    print(f"Frame_00123区域1的'八'牌: {len(region_1_ba_cards_123)}张")
    for card in region_1_ba_cards_123:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    print(f"Frame_00124区域1的'八'牌: {len(region_1_ba_cards_124)}张")
    for card in region_1_ba_cards_124:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 5. 分析可能的错误原因
    print("\n5️⃣ 可能的错误原因分析")
    print("-" * 40)
    
    if ba_cards_124:
        curr_id = ba_cards_124[0]['twin_id']
        
        # 检查这个ID是否来自区域1
        region_1_match = any(card['twin_id'] == curr_id for card in region_1_ba_cards_123)
        if region_1_match:
            print("❌ 确认：区域6的'八'牌错误继承了区域1的ID")
            print("   这说明跨区域继承逻辑被错误触发")
        
        # 检查区域6是否应该有前一帧数据
        if ba_cards_123:
            print("✅ 区域6确实有前一帧数据，应该进行区域内继承")
            print("   问题可能在于：")
            print("   1. 前一帧映射构建错误")
            print("   2. 区域6优先级继承逻辑被跳过")
            print("   3. 基础标签匹配没有生效")

def suggest_debugging_steps():
    """建议下一步调试方案"""
    print("\n🛠️ 建议的调试步骤")
    print("=" * 80)
    
    print("1️⃣ 添加详细日志到SimpleInheritor")
    print("   - 在_process_region_6_priority_inheritance方法开始处添加日志")
    print("   - 记录查找键和前一帧映射的内容")
    print("   - 确认是否进入基础标签匹配逻辑")
    
    print("\n2️⃣ 检查前一帧映射的构建")
    print("   - 在_update_previous_frame方法中添加详细日志")
    print("   - 确认Frame_00123的区域6数据是否正确添加")
    print("   - 检查映射键的格式是否正确")
    
    print("\n3️⃣ 追踪区域6的处理流程")
    print("   - 确认区域6是否进入优先级继承逻辑")
    print("   - 检查是否被跨区域继承逻辑错误处理")
    print("   - 验证基础标签匹配的执行情况")
    
    print("\n4️⃣ 对比GitHub Desktop老版本的修复")
    print("   - 查看老版本中区域6继承的具体实现")
    print("   - 分析老版本修复的核心逻辑")
    print("   - 评估是否可以借鉴其思路")

def create_targeted_fix():
    """创建针对性的修复方案"""
    print("\n🎯 针对性修复方案")
    print("=" * 80)
    
    print("基于分析结果，建议的修复方向：")
    
    print("\n方案A：强制区域6内部继承")
    print("   - 在区域6处理时，强制查找区域6的前一帧数据")
    print("   - 即使标签不完全匹配，也优先从区域6继承")
    print("   - 避免进入跨区域继承逻辑")
    
    print("\n方案B：修复前一帧映射的键格式")
    print("   - 检查_extract_original_label方法是否正确")
    print("   - 确保前一帧映射的键格式与查找键一致")
    print("   - 可能需要统一标签格式化逻辑")
    
    print("\n方案C：增强基础标签匹配")
    print("   - 确保基础标签匹配逻辑被正确执行")
    print("   - 添加更详细的日志和调试信息")
    print("   - 可能需要调整匹配优先级")

if __name__ == "__main__":
    setup_debug_logging()
    analyze_frame_124_inheritance()
    suggest_debugging_steps()
    create_targeted_fix()
