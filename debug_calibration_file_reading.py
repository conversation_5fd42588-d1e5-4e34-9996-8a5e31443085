#!/usr/bin/env python3
"""
调试CalibrationGTFinalProcessor的文件读取过程
检查Frame_00124是否被正确读取
"""

import json
import sys
from pathlib import Path
import glob

def debug_file_reading():
    """调试文件读取过程"""
    print("🔍 调试CalibrationGTFinalProcessor的文件读取过程")
    print("=" * 80)
    
    # 检查输入目录
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 目录存在: {input_dir.exists()}")
    
    if not input_dir.exists():
        print("❌ 输入目录不存在")
        return False
    
    # 获取所有标注文件
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    print(f"📊 找到标注文件: {len(label_files)}个")
    
    # 查找Frame_00124
    frame_124_files = [f for f in label_files if "frame_00124" in f]
    print(f"📊 Frame_00124文件: {len(frame_124_files)}个")
    
    if not frame_124_files:
        print("❌ 没有找到Frame_00124文件")
        return False
    
    frame_124_file = frame_124_files[0]
    print(f"📁 Frame_00124文件路径: {frame_124_file}")
    
    # 读取Frame_00124文件
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 分析区域分布
    region_stats = {}
    region_6_shapes = []
    
    for shape in frame_124_data.get('shapes', []):
        group_id = shape.get('group_id')
        label = shape.get('label', '')
        
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(label)
        
        if group_id == 6:
            region_6_shapes.append(shape)
    
    print(f"\n📊 Frame_00124原始数据区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"  区域{region_id}: {len(labels)}张 - {labels}")
    
    print(f"\n📊 区域6详细数据:")
    if region_6_shapes:
        for i, shape in enumerate(region_6_shapes):
            label = shape.get('label', '')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"  卡牌{i+1}: {label} 位置=({x:.1f}, {y:.1f})")
    else:
        print("  ❌ 没有区域6数据")
    
    # 检查文件在处理序列中的位置
    frame_124_index = None
    for i, file_path in enumerate(label_files):
        if "frame_00124" in file_path:
            frame_124_index = i
            break
    
    print(f"\n📊 Frame_00124在处理序列中的位置:")
    print(f"  文件索引: {frame_124_index} (从0开始)")
    print(f"  处理帧号: 第{frame_124_index + 1}帧")
    
    return len(region_6_shapes) > 0

def simulate_data_conversion():
    """模拟CalibrationGTFinalProcessor的数据转换过程"""
    print("\n🔄 模拟CalibrationGTFinalProcessor的数据转换过程")
    print("=" * 80)
    
    # 读取Frame_00124
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 模拟_analyze_original_shapes方法
    valid_card_labels = [
        "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
        "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
        "暗"
    ]
    
    card_shapes = []
    non_card_shapes = []
    
    for shape in frame_124_data.get("shapes", []):
        label = shape.get("label", "")
        if label in valid_card_labels:
            card_shapes.append(shape)
        else:
            non_card_shapes.append(shape)
    
    print(f"📊 数据分类结果:")
    print(f"  有效卡牌: {len(card_shapes)}张")
    print(f"  非卡牌: {len(non_card_shapes)}张")
    
    # 检查区域6的分类
    region_6_card_shapes = [s for s in card_shapes if s.get('group_id') == 6]
    region_6_non_card_shapes = [s for s in non_card_shapes if s.get('group_id') == 6]
    
    print(f"\n📊 区域6分类结果:")
    print(f"  区域6有效卡牌: {len(region_6_card_shapes)}张")
    print(f"  区域6非卡牌: {len(region_6_non_card_shapes)}张")
    
    if region_6_card_shapes:
        print(f"  区域6有效卡牌详情:")
        for i, shape in enumerate(region_6_card_shapes):
            label = shape.get('label', '')
            print(f"    卡牌{i+1}: {label}")
    
    if region_6_non_card_shapes:
        print(f"  区域6非卡牌详情:")
        for i, shape in enumerate(region_6_non_card_shapes):
            label = shape.get('label', '')
            print(f"    非卡牌{i+1}: {label}")
    
    # 模拟检测数据转换
    detections = []
    for shape in card_shapes:
        # 模拟_convert_shape_to_detection
        points = shape.get('points', [])
        
        if points and len(points) >= 2:
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
        else:
            bbox = [0, 0, 100, 100]
        
        detection = {
            'label': shape.get('label', ''),
            'bbox': bbox,
            'confidence': 1.0,
            'group_id': shape.get('group_id'),
            'points': points
        }
        detections.append(detection)
    
    # 检查转换后的区域6数据
    region_6_detections = [d for d in detections if d.get('group_id') == 6]
    
    print(f"\n📊 转换后的区域6数据:")
    print(f"  区域6检测数据: {len(region_6_detections)}条")
    
    if region_6_detections:
        for i, detection in enumerate(region_6_detections):
            label = detection.get('label', '')
            bbox = detection.get('bbox', [])
            print(f"    检测{i+1}: {label} bbox={bbox}")
    
    return len(region_6_detections) > 0

def main():
    """主函数"""
    print("🚀 CalibrationGTFinalProcessor文件读取调试")
    print("🎯 目标: 检查Frame_00124的区域6数据是否被正确读取")
    print("=" * 80)
    
    # 步骤1: 调试文件读取
    if not debug_file_reading():
        print("❌ 原始文件中没有区域6数据")
        return False
    
    # 步骤2: 模拟数据转换
    if not simulate_data_conversion():
        print("❌ 数据转换后丢失了区域6数据")
        return False
    
    print("\n🎯 调试结论:")
    print("=" * 80)
    print("✅ 原始文件包含区域6数据")
    print("✅ 数据转换保留了区域6数据")
    print("\n🚨 问题可能出现在:")
    print("  1. CalibrationGTFinalProcessor的实际处理逻辑与模拟不符")
    print("  2. 文件读取顺序或文件名匹配有问题")
    print("  3. 某个处理模块过滤了区域6数据")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 文件读取调试完成")
    else:
        print(f"\n❌ 文件读取调试发现问题")
    
    sys.exit(0 if success else 1)
