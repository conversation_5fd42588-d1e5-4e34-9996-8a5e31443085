#!/usr/bin/env python3
"""
调试Frame_00124是否被处理
检查文件处理顺序和匹配逻辑
"""

import json
import sys
from pathlib import Path
import glob

def check_frame_124_processing():
    """检查Frame_00124是否被处理"""
    print("🔍 检查Frame_00124是否被处理")
    print("=" * 80)
    
    # 获取所有标注文件
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    
    print(f"📊 总文件数: {len(label_files)}")
    
    # 查找Frame_00124
    frame_124_found = False
    frame_124_index = None
    
    for i, file_path in enumerate(label_files):
        file_name = Path(file_path).name
        if "frame_00124" in file_name:
            frame_124_found = True
            frame_124_index = i
            print(f"✅ 找到Frame_00124: {file_path}")
            print(f"📊 在处理序列中的位置: 第{i+1}帧 (索引{i})")
            break
    
    if not frame_124_found:
        print("❌ 没有找到Frame_00124文件")
        return False
    
    # 检查Frame_00124的内容
    frame_124_file = label_files[frame_124_index]
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    print(f"\n📊 Frame_00124内容分析:")
    print(f"  总shapes数: {len(frame_124_data.get('shapes', []))}")
    
    # 统计区域分布
    region_stats = {}
    for shape in frame_124_data.get('shapes', []):
        group_id = shape.get('group_id')
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(shape.get('label', ''))
    
    print(f"  区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"    区域{region_id}: {len(labels)}张 - {labels}")
    
    # 特别检查区域6
    region_6_shapes = [s for s in frame_124_data.get('shapes', []) if s.get('group_id') == 6]
    print(f"\n📊 区域6详细信息:")
    print(f"  区域6卡牌数: {len(region_6_shapes)}")
    
    if region_6_shapes:
        for i, shape in enumerate(region_6_shapes):
            label = shape.get('label', '')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"    卡牌{i+1}: {label} 位置=({x:.1f}, {y:.1f})")
    else:
        print("    ❌ 没有区域6卡牌")
    
    return True

def check_processing_order():
    """检查处理顺序"""
    print("\n🔍 检查处理顺序")
    print("=" * 80)
    
    # 获取所有标注文件
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    
    print(f"📊 文件处理顺序 (前10个和后10个):")
    
    # 显示前10个文件
    print(f"  前10个文件:")
    for i in range(min(10, len(label_files))):
        file_name = Path(label_files[i]).name
        print(f"    第{i+1}帧: {file_name}")
    
    # 显示后10个文件
    print(f"  后10个文件:")
    start_index = max(0, len(label_files) - 10)
    for i in range(start_index, len(label_files)):
        file_name = Path(label_files[i]).name
        print(f"    第{i+1}帧: {file_name}")
    
    # 查找Frame_00124的位置
    for i, file_path in enumerate(label_files):
        if "frame_00124" in file_path:
            print(f"\n📊 Frame_00124位置:")
            print(f"  第{i+1}帧: {Path(file_path).name}")
            
            # 显示前后几帧
            print(f"  前后帧对比:")
            for j in range(max(0, i-2), min(len(label_files), i+3)):
                file_name = Path(label_files[j]).name
                marker = " ← Frame_00124" if j == i else ""
                print(f"    第{j+1}帧: {file_name}{marker}")
            break

def check_output_files():
    """检查输出文件"""
    print("\n🔍 检查输出文件")
    print("=" * 80)
    
    # 检查输出目录
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    if not output_dir.exists():
        print("❌ 输出目录不存在")
        return False
    
    # 获取所有输出文件
    output_files = sorted(glob.glob(str(output_dir / "*.json")))
    print(f"📊 输出文件数: {len(output_files)}")
    
    # 查找Frame_00124输出
    frame_124_output = None
    for file_path in output_files:
        if "frame_00124" in file_path:
            frame_124_output = file_path
            break
    
    if frame_124_output:
        print(f"✅ 找到Frame_00124输出: {frame_124_output}")
        
        # 检查输出内容
        with open(frame_124_output, 'r', encoding='utf-8') as f:
            output_data = json.load(f)
        
        print(f"📊 Frame_00124输出分析:")
        print(f"  总shapes数: {len(output_data.get('shapes', []))}")
        
        # 统计区域分布
        region_stats = {}
        for shape in output_data.get('shapes', []):
            group_id = shape.get('group_id')
            if group_id not in region_stats:
                region_stats[group_id] = []
            region_stats[group_id].append(shape.get('label', ''))
        
        print(f"  区域分布:")
        for region_id, labels in sorted(region_stats.items()):
            print(f"    区域{region_id}: {len(labels)}张 - {labels}")
        
        # 检查数字孪生ID
        twin_ids = []
        for shape in output_data.get('shapes', []):
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            if twin_id:
                twin_ids.append(twin_id)
        
        print(f"  数字孪生ID数: {len(twin_ids)}")
        
        # 检查重复ID
        duplicate_ids = []
        seen_ids = set()
        for twin_id in twin_ids:
            if twin_id in seen_ids:
                duplicate_ids.append(twin_id)
            else:
                seen_ids.add(twin_id)
        
        if duplicate_ids:
            print(f"  ❌ 发现重复ID: {duplicate_ids}")
        else:
            print(f"  ✅ 没有重复ID")
        
    else:
        print("❌ 没有找到Frame_00124输出")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 Frame_00124处理调试")
    print("🎯 目标: 检查Frame_00124是否被正确处理")
    print("=" * 80)
    
    # 步骤1: 检查Frame_00124是否存在
    if not check_frame_124_processing():
        print("❌ Frame_00124文件检查失败")
        return False
    
    # 步骤2: 检查处理顺序
    check_processing_order()
    
    # 步骤3: 检查输出文件
    if not check_output_files():
        print("❌ Frame_00124输出检查失败")
        return False
    
    print("\n🎯 调试结论:")
    print("=" * 80)
    print("✅ Frame_00124文件存在且包含区域6数据")
    print("✅ Frame_00124在处理序列中")
    print("✅ Frame_00124有输出文件")
    print("\n🚨 问题可能出现在:")
    print("  1. CalibrationGTFinalProcessor的调试信息没有正确显示")
    print("  2. Frame_00124被处理但调试信息被过滤了")
    print("  3. 文件处理过程中的某个环节有问题")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ Frame_00124处理调试完成")
    else:
        print(f"\n❌ Frame_00124处理调试发现问题")
    
    sys.exit(0 if success else 1)
