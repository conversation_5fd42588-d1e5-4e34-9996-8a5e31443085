#!/usr/bin/env python3
"""
调试前一帧映射的内容
"""

import json
import sys
sys.path.append('src')

from modules.phase2_integrator import Phase2Integrator

def debug_previous_frame_mapping():
    """调试前一帧映射的内容"""
    print("🔍 调试前一帧映射的内容")
    print("=" * 50)
    
    # 创建Phase2Integrator实例
    integrator = Phase2Integrator()
    
    # 加载Frame_00122和Frame_00123的原始数据
    frame_122_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00122.json"
    frame_123_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00123.json"
    frame_124_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00124.json"
    
    with open(frame_122_path, 'r', encoding='utf-8') as f:
        frame_122_data = json.load(f)
    
    with open(frame_123_path, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    with open(frame_124_path, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 处理Frame_00122
    print("🔄 处理Frame_00122...")
    result_122 = integrator.process_frame(frame_122_data.get('shapes', []))
    
    # 处理Frame_00123
    print("🔄 处理Frame_00123...")
    result_123 = integrator.process_frame(frame_123_data.get('shapes', []))
    
    # 检查前一帧映射中区域6的内容
    print("\n🔍 Frame_00123处理后的前一帧映射中区域6的内容:")
    inheritor = integrator.inheritor
    if hasattr(inheritor, 'previous_frame_mapping'):
        region_6_mappings = {}
        for key, cards_list in inheritor.previous_frame_mapping.items():
            if key[0] == 6:  # 区域6
                region_6_mappings[key] = cards_list
        
        if region_6_mappings:
            for key, cards_list in region_6_mappings.items():
                region_id, label = key
                print(f"  键: {key}")
                for card in cards_list:
                    print(f"    卡牌: {card.get('label')} -> ID: {card.get('twin_id')}")
        else:
            print("  没有区域6的映射")
    else:
        print("  无法访问前一帧映射")
    
    # 现在处理Frame_00124，看看会发生什么
    print("\n🔄 处理Frame_00124...")
    
    # 在处理之前，检查当前帧的区域6卡牌
    print("Frame_00124当前帧区域6的卡牌:")
    for shape in frame_124_data.get('shapes', []):
        if shape.get('group_id') == 6:
            print(f"  {shape.get('label')}")
    
    result_124 = integrator.process_frame(frame_124_data.get('shapes', []))
    
    # 检查结果
    if result_124.success:
        print("\nFrame_00124处理结果中区域6的'八'牌:")
        for card in result_124.cards:
            if card.get('group_id') == 6 and '八' in card.get('label', ''):
                print(f"  {card.get('label')} -> ID: {card.get('twin_id')}")

if __name__ == "__main__":
    debug_previous_frame_mapping()
