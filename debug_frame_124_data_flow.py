#!/usr/bin/env python3
"""
调试Frame_00124的数据流
追踪区域6数据在处理链路中的流向
"""

import json
import sys
from pathlib import Path

def debug_original_data():
    """调试原始数据"""
    print("🔍 调试Frame_00124原始数据")
    print("=" * 80)
    
    # 读取原始Frame_00124数据
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    
    if not frame_124_file.exists():
        print("❌ 原始frame_00124.json不存在")
        return False
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 统计区域分布
    region_stats = {}
    region_6_cards = []
    
    for shape in frame_124_data.get('shapes', []):
        group_id = shape.get('group_id')
        label = shape.get('label', '')
        
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(label)
        
        if group_id == 6:
            region_6_cards.append({
                'label': label,
                'points': shape.get('points', []),
                'shape_type': shape.get('shape_type', ''),
                'flags': shape.get('flags', {})
            })
    
    print(f"📊 原始数据区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"  区域{region_id}: {len(labels)}张 - {labels}")
    
    print(f"\n📊 区域6详细数据:")
    for i, card in enumerate(region_6_cards):
        points = card['points']
        x, y = points[0] if points else (0, 0)
        print(f"  卡牌{i+1}: {card['label']} 位置=({x:.1f}, {y:.1f})")
    
    return len(region_6_cards) > 0

def simulate_data_conversion():
    """模拟数据转换过程"""
    print("\n🔄 模拟数据转换过程")
    print("=" * 80)
    
    # 读取原始数据
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 模拟CalibrationGTFinalProcessor的数据转换
    detections = []
    
    for shape in frame_124_data.get('shapes', []):
        # 模拟_convert_shape_to_detection方法
        points = shape.get('points', [])
        
        if points and len(points) >= 2:
            # 计算边界框
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
        else:
            bbox = [0, 0, 100, 100]  # 默认bbox
        
        detection = {
            'label': shape.get('label', ''),
            'bbox': bbox,
            'confidence': 1.0,  # 标注数据默认置信度
            'group_id': shape.get('group_id'),
            'points': points
        }
        
        detections.append(detection)
    
    # 统计转换后的数据
    region_stats = {}
    region_6_detections = []
    
    for detection in detections:
        group_id = detection.get('group_id')
        label = detection.get('label', '')
        
        if group_id not in region_stats:
            region_stats[group_id] = []
        region_stats[group_id].append(label)
        
        if group_id == 6:
            region_6_detections.append(detection)
    
    print(f"📊 转换后数据区域分布:")
    for region_id, labels in sorted(region_stats.items()):
        print(f"  区域{region_id}: {len(labels)}张 - {labels}")
    
    print(f"\n📊 区域6转换后数据:")
    for i, detection in enumerate(region_6_detections):
        bbox = detection['bbox']
        print(f"  检测{i+1}: {detection['label']} bbox={bbox}")
    
    return region_6_detections

def simulate_data_validation(detections):
    """模拟数据验证过程"""
    print("\n✅ 模拟数据验证过程")
    print("=" * 80)
    
    # 模拟DataValidator的验证逻辑
    required_fields = ['label', 'bbox', 'confidence', 'group_id']
    valid_labels = [
        "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
        "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
        "暗"
    ]
    valid_regions = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
    
    cleaned_data = []
    errors = []
    warnings = []
    
    for i, detection in enumerate(detections):
        # 检查必需字段
        missing_fields = [field for field in required_fields if field not in detection]
        if missing_fields:
            errors.append(f"检测{i}: 缺少字段 {missing_fields}")
            continue
        
        # 检查标签
        label = detection.get('label')
        if label not in valid_labels:
            warnings.append(f"检测{i}: 未知标签 {label}")
        
        # 检查区域ID
        group_id = detection.get('group_id')
        if group_id not in valid_regions:
            warnings.append(f"检测{i}: 未知区域ID {group_id}")
        
        # 清理数据
        cleaned_detection = detection.copy()
        cleaned_data.append(cleaned_detection)
    
    # 统计验证后的数据
    region_6_cleaned = [d for d in cleaned_data if d.get('group_id') == 6]
    
    print(f"📊 验证结果:")
    print(f"  输入: {len(detections)}条检测")
    print(f"  输出: {len(cleaned_data)}条有效数据")
    print(f"  错误: {len(errors)}个")
    print(f"  警告: {len(warnings)}个")
    print(f"  区域6: {len(region_6_cleaned)}张卡牌")
    
    if errors:
        print(f"\n❌ 错误:")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print(f"\n⚠️ 警告:")
        for warning in warnings:
            print(f"  {warning}")
    
    return cleaned_data

def main():
    """主函数"""
    print("🚀 Frame_00124数据流调试")
    print("🎯 目标: 追踪区域6数据在处理链路中的流向")
    print("=" * 80)
    
    # 步骤1: 调试原始数据
    if not debug_original_data():
        print("❌ 原始数据中没有区域6")
        return False
    
    # 步骤2: 模拟数据转换
    region_6_detections = simulate_data_conversion()
    if not region_6_detections:
        print("❌ 数据转换后丢失了区域6")
        return False
    
    # 步骤3: 模拟数据验证
    all_detections = []
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    for shape in frame_124_data.get('shapes', []):
        points = shape.get('points', [])
        if points and len(points) >= 2:
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
        else:
            bbox = [0, 0, 100, 100]
        
        detection = {
            'label': shape.get('label', ''),
            'bbox': bbox,
            'confidence': 1.0,
            'group_id': shape.get('group_id'),
            'points': points
        }
        all_detections.append(detection)
    
    cleaned_data = simulate_data_validation(all_detections)
    region_6_cleaned = [d for d in cleaned_data if d.get('group_id') == 6]
    
    if not region_6_cleaned:
        print("❌ 数据验证后丢失了区域6")
        return False
    
    print("\n🎯 调试结论:")
    print("=" * 80)
    print("✅ 原始数据包含区域6")
    print("✅ 数据转换保留了区域6")
    print("✅ 数据验证保留了区域6")
    print("\n🚨 问题可能出现在:")
    print("  1. 实际的数据读取过程")
    print("  2. CalibrationGTFinalProcessor的数据转换逻辑")
    print("  3. 某个模块的实际处理逻辑与预期不符")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 数据流调试完成")
    else:
        print(f"\n❌ 数据流调试发现问题")
    
    sys.exit(0 if success else 1)
