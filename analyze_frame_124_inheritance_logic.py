#!/usr/bin/env python3
"""
深度分析Frame_00124的继承逻辑问题
找出为什么区域6的"八"没有正确继承"2八"而是继承了"1八"
"""

import json
import sys
from pathlib import Path
from collections import defaultdict

def analyze_inheritance_logic():
    """分析继承逻辑问题"""
    print("🔍 深度分析Frame_00124的继承逻辑问题")
    print("=" * 80)
    
    # 读取Frame_00123和Frame_00124数据
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    frame_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    with open(frame_123_file, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    print("📊 Frame_00123中的'八'分布:")
    frame_123_ba_cards = []
    for shape in frame_123_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            card_info = {
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'points': shape.get('points', []),
                'region_name': shape.get('region_name', '')
            }
            frame_123_ba_cards.append(card_info)
            print(f"  区域{card_info['region']} ({card_info['region_name']}): {card_info['label']} → {card_info['twin_id']}")
    
    print("\n📊 Frame_00124中的'八'分布:")
    frame_124_ba_cards = []
    for shape in frame_124_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            card_info = {
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'points': shape.get('points', []),
                'region_name': shape.get('region_name', '')
            }
            frame_124_ba_cards.append(card_info)
            print(f"  区域{card_info['region']} ({card_info['region_name']}): {card_info['label']} → {card_info['twin_id']}")
    
    print("\n🔍 继承关系分析:")
    print("Frame_00123 → Frame_00124 的继承关系:")
    
    # 分析区域1的继承
    region_1_123 = [card for card in frame_123_ba_cards if card['region'] == 1]
    region_1_124 = [card for card in frame_124_ba_cards if card['region'] == 1]
    
    if region_1_123 and region_1_124:
        print(f"✅ 区域1继承: {region_1_123[0]['twin_id']} → {region_1_124[0]['twin_id']} (正确)")
    
    # 分析区域6的继承
    region_6_123 = [card for card in frame_123_ba_cards if card['region'] == 6]
    region_6_124 = [card for card in frame_124_ba_cards if card['region'] == 6]
    
    if region_6_123 and region_6_124:
        expected_id = region_6_123[0]['twin_id']
        actual_id = region_6_124[0]['twin_id']
        print(f"❌ 区域6继承: 期望{expected_id} → 实际{actual_id} (错误！)")
        print(f"   问题：区域6应该继承自己区域的'{expected_id}'，但实际继承了'{actual_id}'")
    
    # 分析区域2的新卡牌
    region_2_124 = [card for card in frame_124_ba_cards if card['region'] == 2]
    if region_2_124:
        print(f"✅ 区域2新卡牌: {region_2_124[0]['twin_id']} (正确)")
    
    return frame_123_ba_cards, frame_124_ba_cards

def analyze_position_data(frame_123_ba_cards, frame_124_ba_cards):
    """分析位置数据，理解空间排序逻辑"""
    print("\n🎯 位置数据分析:")
    print("=" * 80)
    
    def get_position(card):
        points = card.get('points', [])
        if points and len(points) > 0:
            return points[0][0], points[0][1]  # x, y
        return 0, 0
    
    print("📍 Frame_00123位置数据:")
    for card in frame_123_ba_cards:
        x, y = get_position(card)
        print(f"  区域{card['region']}: {card['twin_id']} 位置=({x:.1f}, {y:.1f})")
    
    print("\n📍 Frame_00124位置数据:")
    for card in frame_124_ba_cards:
        x, y = get_position(card)
        print(f"  区域{card['region']}: {card['twin_id']} 位置=({x:.1f}, {y:.1f})")
    
    # 分析区域6的位置变化
    region_6_123 = [card for card in frame_123_ba_cards if card['region'] == 6]
    region_6_124 = [card for card in frame_124_ba_cards if card['region'] == 6]
    
    if region_6_123 and region_6_124:
        x1, y1 = get_position(region_6_123[0])
        x2, y2 = get_position(region_6_124[0])
        dx, dy = x2 - x1, y2 - y1
        print(f"\n🔍 区域6位置变化:")
        print(f"  Frame_00123: ({x1:.1f}, {y1:.1f})")
        print(f"  Frame_00124: ({x2:.1f}, {y2:.1f})")
        print(f"  位移: dx={dx:.1f}, dy={dy:.1f}")
        
        # 判断是否应该能匹配
        distance = (dx**2 + dy**2)**0.5
        print(f"  距离: {distance:.1f}像素")
        if distance < 50:  # 假设50像素内应该能匹配
            print(f"  ✅ 位置变化较小，应该能够匹配")
        else:
            print(f"  ❌ 位置变化较大，可能导致匹配失败")

def analyze_cross_region_inheritance():
    """分析跨区域继承规则"""
    print("\n🔗 跨区域继承规则分析:")
    print("=" * 80)
    
    # 从代码中提取的跨区域继承规则
    cross_region_rules = {
        6: [1, 3, 4, 7, 8],  # 观战方吃碰区可以从多个区域继承
    }
    
    print("📋 区域6的跨区域继承规则:")
    print(f"  区域6可以从以下区域继承: {cross_region_rules[6]}")
    print(f"  优先级顺序: 1(手牌) > 3(抓牌) > 4(打牌) > 7(对战方抓牌) > 8(对战方调整)")
    
    print("\n🚨 问题分析:")
    print("  1. 区域6应该优先从自己区域(6→6)继承")
    print("  2. 如果本区域继承失败，才考虑跨区域继承")
    print("  3. 但实际上区域6的'八'继承了区域1的'1八'")
    print("  4. 这说明区域6的本区域继承逻辑可能有问题")

def analyze_inheritance_order():
    """分析继承处理顺序"""
    print("\n⚡ 继承处理顺序分析:")
    print("=" * 80)
    
    print("📋 当前的处理顺序:")
    print("  1. 吃碰区域特殊处理 (区域6, 16)")
    print("     - _process_eating_region_inheritance")
    print("     - 区域6使用_process_region_6_priority_inheritance")
    print("  2. 其他区域的标签分组处理")
    print("  3. 跨区域继承处理")
    
    print("\n🚨 可能的问题:")
    print("  1. 区域6的本区域继承可能失败")
    print("  2. 失败后的卡牌被标记为新卡牌")
    print("  3. 但在后续的跨区域继承阶段，这些卡牌又被处理")
    print("  4. 跨区域继承时错误地匹配了区域1的'1八'")

def main():
    """主函数"""
    print("🚀 Frame_00124继承逻辑深度分析")
    print("🎯 目标: 找出区域6的'八'为什么继承了错误的ID")
    print("=" * 80)
    
    # 步骤1: 分析继承关系
    frame_123_ba_cards, frame_124_ba_cards = analyze_inheritance_logic()
    
    # 步骤2: 分析位置数据
    analyze_position_data(frame_123_ba_cards, frame_124_ba_cards)
    
    # 步骤3: 分析跨区域继承规则
    analyze_cross_region_inheritance()
    
    # 步骤4: 分析继承处理顺序
    analyze_inheritance_order()
    
    print("\n🎯 结论和修复建议:")
    print("=" * 80)
    print("🔍 问题根源:")
    print("  1. 区域6的本区域继承(_process_region_6_priority_inheritance)可能失败")
    print("  2. 失败的卡牌在跨区域继承阶段被错误处理")
    print("  3. 跨区域继承时优先匹配了区域1的'1八'而不是区域6的'2八'")
    
    print("\n🔧 修复方案:")
    print("  1. 检查区域6的空间排序和匹配算法")
    print("  2. 确保区域6的本区域继承优先级最高")
    print("  3. 修复跨区域继承的优先级逻辑")
    print("  4. 添加更强的ID冲突检测机制")

if __name__ == "__main__":
    main()
