#!/usr/bin/env python3
"""
检查第58帧对应的实际文件
"""

import json
import sys
from pathlib import Path
import glob

def check_frame_58():
    """检查第58帧对应的文件"""
    print("🔍 检查第58帧对应的实际文件")
    print("=" * 80)
    
    # 获取所有标注文件
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    
    print(f"📊 总文件数: {len(label_files)}")
    
    # 第58帧（从0开始计数，所以是索引57）
    if len(label_files) > 57:
        frame_58_file = label_files[57]
        print(f"📁 第58帧文件: {frame_58_file}")
        
        # 读取文件
        with open(frame_58_file, 'r', encoding='utf-8') as f:
            frame_58_data = json.load(f)
        
        # 分析区域分布
        region_stats = {}
        region_6_shapes = []
        
        for shape in frame_58_data.get('shapes', []):
            group_id = shape.get('group_id')
            label = shape.get('label', '')
            
            if group_id not in region_stats:
                region_stats[group_id] = []
            region_stats[group_id].append(label)
            
            if group_id == 6:
                region_6_shapes.append(shape)
        
        print(f"\n📊 第58帧区域分布:")
        for region_id, labels in sorted(region_stats.items()):
            print(f"  区域{region_id}: {len(labels)}张 - {labels}")
        
        print(f"\n📊 第58帧区域6数据:")
        if region_6_shapes:
            for i, shape in enumerate(region_6_shapes):
                label = shape.get('label', '')
                points = shape.get('points', [])
                x, y = points[0] if points else (0, 0)
                print(f"  卡牌{i+1}: {label} 位置=({x:.1f}, {y:.1f})")
        else:
            print("  ❌ 没有区域6数据")
        
        return len(region_6_shapes) > 0
    else:
        print("❌ 文件数量不足，没有第58帧")
        return False

def find_frame_124_actual_position():
    """找到Frame_00124的实际处理位置"""
    print("\n🔍 查找Frame_00124的实际处理位置")
    print("=" * 80)
    
    # 获取所有标注文件
    input_dir = Path("legacy_assets/ceshi/calibration_gt/labels")
    label_files = sorted(glob.glob(str(input_dir / "*.json")))
    
    # 查找Frame_00124
    for i, file_path in enumerate(label_files):
        if "frame_00124" in file_path:
            print(f"📁 Frame_00124文件: {file_path}")
            print(f"📊 在处理序列中的位置: 第{i+1}帧 (索引{i})")
            
            # 检查前后几帧
            print(f"\n📊 前后帧对比:")
            for j in range(max(0, i-2), min(len(label_files), i+3)):
                file_name = Path(label_files[j]).name
                marker = " ← Frame_00124" if j == i else ""
                print(f"  第{j+1}帧: {file_name}{marker}")
            
            return i
    
    print("❌ 没有找到Frame_00124")
    return None

def main():
    """主函数"""
    print("🚀 第58帧文件检查")
    print("🎯 目标: 确认第58帧是否是Frame_00124")
    print("=" * 80)
    
    # 步骤1: 检查第58帧
    frame_58_has_region_6 = check_frame_58()
    
    # 步骤2: 找到Frame_00124的实际位置
    frame_124_position = find_frame_124_actual_position()
    
    print("\n🎯 检查结论:")
    print("=" * 80)
    
    if frame_124_position is not None:
        if frame_124_position == 57:  # 第58帧（索引57）
            print("✅ 第58帧确实是Frame_00124")
            if frame_58_has_region_6:
                print("✅ Frame_00124包含区域6数据")
                print("🚨 问题出现在CalibrationGTFinalProcessor的处理过程中")
            else:
                print("❌ Frame_00124不包含区域6数据")
        else:
            print(f"❌ 第58帧不是Frame_00124")
            print(f"   Frame_00124实际是第{frame_124_position + 1}帧")
            print(f"   CalibrationGTFinalProcessor处理的第58帧是其他文件")
    else:
        print("❌ 没有找到Frame_00124文件")

if __name__ == "__main__":
    main()
