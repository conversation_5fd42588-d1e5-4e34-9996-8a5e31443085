#!/usr/bin/env python3
"""
专门测试Frame_00123和Frame_00124的处理
"""

import os
import sys
import json
import logging

# 添加src目录到路径
sys.path.append('src')

# 直接导入Phase2Integrator
from modules.phase2_integrator import Phase2Integrator

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_specific_frames():
    """测试特定帧"""
    print("🔍 测试Frame_00123和Frame_00124的处理")
    print("=" * 60)
    
    # 输入和输出路径
    input_dir = "legacy_assets/ceshi/calibration_gt/labels"
    output_dir = "output/debug_frames"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建Phase2Integrator
    integrator = Phase2Integrator()
    
    # 处理Frame_00123
    print("\n🔄 处理Frame_00123...")
    frame_123_path = os.path.join(input_dir, "frame_00123.json")
    
    if os.path.exists(frame_123_path):
        with open(frame_123_path, 'r', encoding='utf-8') as f:
            frame_123_data = json.load(f)
        
        # 转换为检测格式
        detections_123 = []
        for shape in frame_123_data.get('shapes', []):
            # 从points提取bbox，或使用默认值
            points = shape.get('points', [[0, 0], [100, 100]])
            if len(points) >= 2:
                x1, y1 = points[0]
                x2, y2 = points[1]
                bbox = [float(x1), float(y1), float(x2), float(y2)]
            else:
                bbox = [0.0, 0.0, 100.0, 100.0]

            detection = {
                'label': shape.get('label'),
                'bbox': bbox,
                'confidence': 0.9,  # 默认置信度
                'group_id': shape.get('group_id'),
                'region_name': shape.get('region_name')
            }
            detections_123.append(detection)
        
        print(f"Frame_00123输入: {len(detections_123)}个检测")
        
        # 处理
        result_123 = integrator.process_frame(detections_123)
        
        if result_123.success:
            print(f"Frame_00123处理成功: {len(result_123.processed_cards)}张卡牌")

            # 检查区域6的卡牌
            region_6_cards = [card for card in result_123.processed_cards if card.get('group_id') == 6]
            print(f"Frame_00123区域6: {len(region_6_cards)}张卡牌")
            for card in region_6_cards:
                if '八' in card.get('label', ''):
                    print(f"  {card.get('label')} -> ID: {card.get('twin_id')}")
        else:
            print("Frame_00123处理失败")
            return
    else:
        print(f"Frame_00123文件不存在: {frame_123_path}")
        return
    
    print("\n" + "="*60)
    
    # 处理Frame_00124
    print("\n🔄 处理Frame_00124...")
    frame_124_path = os.path.join(input_dir, "frame_00124.json")
    
    if os.path.exists(frame_124_path):
        with open(frame_124_path, 'r', encoding='utf-8') as f:
            frame_124_data = json.load(f)
        
        # 转换为检测格式
        detections_124 = []
        for shape in frame_124_data.get('shapes', []):
            # 从points提取bbox，或使用默认值
            points = shape.get('points', [[0, 0], [100, 100]])
            if len(points) >= 2:
                x1, y1 = points[0]
                x2, y2 = points[1]
                bbox = [float(x1), float(y1), float(x2), float(y2)]
            else:
                bbox = [0.0, 0.0, 100.0, 100.0]

            detection = {
                'label': shape.get('label'),
                'bbox': bbox,
                'confidence': 0.9,  # 默认置信度
                'group_id': shape.get('group_id'),
                'region_name': shape.get('region_name')
            }
            detections_124.append(detection)
        
        print(f"Frame_00124输入: {len(detections_124)}个检测")
        
        # 处理
        result_124 = integrator.process_frame(detections_124)
        
        if result_124.success:
            print(f"Frame_00124处理成功: {len(result_124.processed_cards)}张卡牌")

            # 检查区域6的卡牌
            region_6_cards = [card for card in result_124.processed_cards if card.get('group_id') == 6]
            print(f"Frame_00124区域6: {len(region_6_cards)}张卡牌")
            for card in region_6_cards:
                if '八' in card.get('label', ''):
                    print(f"  {card.get('label')} -> ID: {card.get('twin_id')}")
                    
                    # 检查继承是否正确
                    expected_id = '2八'
                    actual_id = card.get('twin_id')
                    if actual_id == expected_id:
                        print(f"  ✅ 继承正确！")
                    else:
                        print(f"  ❌ 继承错误！期望: {expected_id}, 实际: {actual_id}")
        else:
            print("Frame_00124处理失败")
    else:
        print(f"Frame_00124文件不存在: {frame_124_path}")

if __name__ == "__main__":
    setup_logging()
    test_specific_frames()
