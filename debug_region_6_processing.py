#!/usr/bin/env python3
"""
调试区域6的处理过程
"""

import json

def debug_region_6():
    """调试区域6的处理"""
    print("🔍 调试区域6的处理过程")
    print("=" * 50)
    
    # 检查Frame_00124的输出结果
    with open("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json", 'r', encoding='utf-8') as f:
        frame_124 = json.load(f)
    
    print("Frame_00124输出结果中区域6的卡牌:")
    region_6_cards = []
    for shape in frame_124.get('shapes', []):
        if shape.get('group_id') == 6:
            region_6_cards.append({
                'label': shape.get('label'),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'region_name': shape.get('region_name')
            })
    
    for card in region_6_cards:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 特别检查'八'牌
    ba_cards = [card for card in region_6_cards if '八' in card['label']]
    if ba_cards:
        card = ba_cards[0]
        print(f"\n🔍 区域6的'八'牌详情:")
        print(f"  标签: {card['label']}")
        print(f"  ID: {card['twin_id']}")
        print(f"  区域名: {card['region_name']}")
        
        if card['twin_id'] == '2八':
            print("  ✅ 继承正确！")
        else:
            print("  ❌ 继承错误！应该是'2八'")
    else:
        print("\n❌ 区域6没有'八'牌")
    
    # 检查Frame_00123的输出结果
    with open("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json", 'r', encoding='utf-8') as f:
        frame_123 = json.load(f)
    
    print(f"\nFrame_00123输出结果中区域6的'八'牌:")
    for shape in frame_123.get('shapes', []):
        if shape.get('group_id') == 6 and '八' in shape.get('label', ''):
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            print(f"  {shape.get('label')} -> ID: {twin_id}")
    
    print(f"\n🔍 分析:")
    print(f"  Frame_00123区域6有'2八' (ID: 2八)")
    print(f"  Frame_00124区域6有'1八' (ID: {ba_cards[0]['twin_id'] if ba_cards else 'None'})")
    print(f"  预期: Frame_00124区域6的'1八'应该继承'2八'的ID")

if __name__ == "__main__":
    debug_region_6()
