#!/usr/bin/env python3
"""
测试标签提取逻辑
"""

import re

def _extract_original_label(processed_label: str) -> str:
    """
    提取原始检测标签

    将分配后的标签还原为原始检测标签：
    - "1一" -> "一"
    - "2二" -> "二"
    - "虚拟三" -> "三"
    - "3四暗" -> "四"
    """
    if not processed_label:
        return processed_label

    # 处理虚拟牌：虚拟三 -> 三
    if processed_label.startswith('虚拟'):
        virtual_match = re.match(r'虚拟(.+)', processed_label)
        if virtual_match:
            return virtual_match.group(1)

    # 处理暗牌：3四暗 -> 四
    if processed_label.endswith('暗'):
        dark_match = re.match(r'\d*(.+)暗', processed_label)
        if dark_match:
            return dark_match.group(1)

    # 处理普通牌：1一 -> 一, 2二 -> 二
    normal_match = re.match(r'\d*(.+)', processed_label)
    if normal_match:
        original = normal_match.group(1)
        # 确保提取的是有效的牌面标签
        valid_labels = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                       "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"]
        if original in valid_labels:
            return original

    # 如果无法提取，返回原标签
    return processed_label

def main():
    test_labels = ['1八', '2八', '八', '3八', '1一', '二', '虚拟三', '4四暗']
    
    print("🧪 标签提取测试:")
    for label in test_labels:
        result = _extract_original_label(label)
        print(f"  '{label}' → '{result}'")

if __name__ == "__main__":
    main()
