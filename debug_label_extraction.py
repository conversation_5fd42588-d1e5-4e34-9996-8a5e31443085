#!/usr/bin/env python3
"""
调试标签提取问题
检查_extract_original_label方法对"八"的处理
"""

import re

def debug_extract_original_label():
    """调试标签提取"""
    print("🔍 调试_extract_original_label方法对'八'的处理")
    print("=" * 80)
    
    def _extract_original_label(processed_label: str) -> str:
        """
        提取原始检测标签
        
        将分配后的标签还原为原始检测标签：
        - "1一" -> "一"
        - "2二" -> "二"
        - "虚拟三" -> "三"
        - "3四暗" -> "四"
        """
        if not processed_label:
            return processed_label

        # 处理虚拟牌：虚拟三 -> 三
        if processed_label.startswith('虚拟'):
            virtual_match = re.match(r'虚拟(.+)', processed_label)
            if virtual_match:
                return virtual_match.group(1)

        # 处理暗牌：3四暗 -> 四
        if processed_label.endswith('暗'):
            dark_match = re.match(r'\d*(.+)暗', processed_label)
            if dark_match:
                return dark_match.group(1)

        # 处理普通牌：1一 -> 一, 2二 -> 二
        normal_match = re.match(r'\d*(.+)', processed_label)
        if normal_match:
            original = normal_match.group(1)
            # 确保提取的是有效的牌面标签
            valid_labels = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                           "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"]
            if original in valid_labels:
                return original

        # 如果无法提取，返回原标签
        return processed_label
    
    # 测试不同的"八"标签
    test_labels = [
        "八",      # 原始标签
        "1八",     # 分配后标签
        "2八",     # 分配后标签
        "3八",     # 分配后标签
    ]
    
    print("📊 标签提取测试:")
    for label in test_labels:
        extracted = _extract_original_label(label)
        print(f"  输入: '{label}' ({label.encode('utf-8')}) → 输出: '{extracted}' ({extracted.encode('utf-8')})")
        print(f"    输入长度: {len(label)}, 输出长度: {len(extracted)}")
        print(f"    提取正确: {extracted == '八'}")
        print()
    
    # 测试编码问题
    print("📊 编码问题测试:")
    ba_utf8 = "八"
    ba_bytes = ba_utf8.encode('utf-8')
    ba_decoded = ba_bytes.decode('utf-8')
    
    print(f"  原始'八': '{ba_utf8}' ({ba_utf8.encode('utf-8')})")
    print(f"  编码后: {ba_bytes}")
    print(f"  解码后: '{ba_decoded}' ({ba_decoded.encode('utf-8')})")
    print(f"  编码一致: {ba_utf8 == ba_decoded}")
    
    # 测试查找键构建
    print("\n📊 查找键构建测试:")
    region_id = 6
    for label in test_labels:
        extracted = _extract_original_label(label)
        lookup_key = (region_id, extracted)
        print(f"  标签'{label}' → 查找键{lookup_key}")
        print(f"    查找键字节: {str(lookup_key).encode('utf-8')}")

if __name__ == "__main__":
    debug_extract_original_label()
