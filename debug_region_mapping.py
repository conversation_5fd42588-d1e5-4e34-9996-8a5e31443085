#!/usr/bin/env python3
"""
调试区域映射问题
"""

import json

def check_region_mapping():
    """检查Frame_00124的区域映射"""
    print("🔍 检查Frame_00124的区域映射")
    print("=" * 50)
    
    # 加载Frame_00124数据
    with open("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json", 'r', encoding='utf-8') as f:
        frame_124 = json.load(f)
    
    print("Frame_00124中的所有区域和卡牌:")
    region_cards = {}
    
    for shape in frame_124.get('shapes', []):
        region_id = shape.get('group_id')
        region_name = shape.get('region_name')
        label = shape.get('label')
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        
        if region_id not in region_cards:
            region_cards[region_id] = []
        
        region_cards[region_id].append({
            'label': label,
            'twin_id': twin_id,
            'region_name': region_name
        })
    
    for region_id in sorted(region_cards.keys()):
        cards = region_cards[region_id]
        region_name = cards[0]['region_name'] if cards else 'Unknown'
        print(f"\n区域{region_id} ({region_name}):")
        for card in cards:
            print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    # 特别检查区域6
    print(f"\n🔍 特别检查区域6:")
    if 6 in region_cards:
        print(f"区域6有{len(region_cards[6])}张卡牌:")
        for card in region_cards[6]:
            print(f"  {card['label']} -> ID: {card['twin_id']}")
    else:
        print("❌ 区域6不存在！")
    
    # 检查'八'牌的分布
    print(f"\n🔍 '八'牌分布:")
    for region_id, cards in region_cards.items():
        for card in cards:
            if '八' in card['label']:
                region_name = card['region_name']
                print(f"  区域{region_id} ({region_name}): {card['label']} -> ID: {card['twin_id']}")

if __name__ == "__main__":
    check_region_mapping()
