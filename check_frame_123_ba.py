#!/usr/bin/env python3
"""
检查Frame_00123的区域6"八"
"""

import json
from pathlib import Path

def check_frame_123_ba():
    """检查Frame_00123的区域6"八" """
    print("🔍 检查Frame_00123的区域6'八'")
    print("=" * 80)
    
    # 检查Frame_00123输出
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    if frame_123_file.exists():
        with open(frame_123_file, 'r', encoding='utf-8') as f:
            frame_123_data = json.load(f)
        
        # 查找区域6的"八"
        region_6_ba = []
        for shape in frame_123_data.get('shapes', []):
            if shape.get('group_id') == 6 and '八' in shape.get('label', ''):
                region_6_ba.append(shape)
        
        print(f"📊 Frame_00123区域6的'八': {len(region_6_ba)}张")
        for i, shape in enumerate(region_6_ba):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"  八{i+1}: 标签='{label}' → ID='{twin_id}' 位置=({x:.1f}, {y:.1f})")
        
        if not region_6_ba:
            print("  ❌ Frame_00123区域6没有'八'")
    else:
        print("❌ Frame_00123输出文件不存在")

if __name__ == "__main__":
    check_frame_123_ba()
