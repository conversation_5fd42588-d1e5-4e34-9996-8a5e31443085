import json

# 检查Frame_00124的区域6"八"的ID分配
data = json.load(open('output/calibration_gt_final_with_digital_twin/labels/frame_00124.json', 'r', encoding='utf-8'))
region_6_ba = [s for s in data.get('shapes', []) if s.get('group_id') == 6 and ('八' in s.get('label', '') or '捌' in s.get('label', ''))]

print('Frame_00124区域6的八:')
for s in region_6_ba:
    label = s.get('label', '')
    twin_id = s.get('attributes', {}).get('digital_twin_id', 'N/A')
    print(f'  标签: {label} → ID: {twin_id}')

# 检查所有区域6的卡牌
print('\nFrame_00124区域6所有卡牌:')
region_6_all = [s for s in data.get('shapes', []) if s.get('group_id') == 6]
for s in region_6_all:
    label = s.get('label', '')
    twin_id = s.get('attributes', {}).get('digital_twin_id', 'N/A')
    print(f'  标签: {label} → ID: {twin_id}')
