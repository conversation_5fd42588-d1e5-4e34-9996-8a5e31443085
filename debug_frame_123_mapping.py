#!/usr/bin/env python3
"""
调试Frame_00123的映射构建情况
检查区域6的映射是否正确构建
"""

import json
import sys
from pathlib import Path

def debug_frame_123_mapping():
    """调试Frame_00123的映射构建"""
    print("🔍 调试Frame_00123的映射构建")
    print("=" * 80)
    
    # 读取Frame_00123数据
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    
    with open(frame_123_file, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    # 模拟映射构建过程
    def _extract_original_label(processed_label: str) -> str:
        """提取原始检测标签"""
        import re
        if not processed_label:
            return processed_label

        # 处理虚拟牌：虚拟三 -> 三
        if processed_label.startswith('虚拟'):
            virtual_match = re.match(r'虚拟(.+)', processed_label)
            if virtual_match:
                return virtual_match.group(1)

        # 处理暗牌：3四暗 -> 四
        if processed_label.endswith('暗'):
            dark_match = re.match(r'\d*(.+)暗', processed_label)
            if dark_match:
                return dark_match.group(1)

        # 处理普通牌：1一 -> 一, 2二 -> 二
        normal_match = re.match(r'\d*(.+)', processed_label)
        if normal_match:
            original = normal_match.group(1)
            # 确保提取的是有效的牌面标签
            valid_labels = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                           "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"]
            if original in valid_labels:
                return original

        # 如果无法提取，返回原标签
        return processed_label
    
    # 构建映射
    previous_frame_mapping = {}
    
    for shape in frame_123_data.get('shapes', []):
        group_id = shape.get('group_id')
        processed_label = shape.get('label')
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        
        if group_id is not None and processed_label is not None:
            # 提取原始标签作为查找键
            original_label = _extract_original_label(processed_label)
            lookup_key = (group_id, original_label)
            
            if lookup_key not in previous_frame_mapping:
                previous_frame_mapping[lookup_key] = []
            
            previous_frame_mapping[lookup_key].append({
                'processed_label': processed_label,
                'twin_id': twin_id,
                'group_id': group_id
            })
            
            # 调试区域6的映射
            if group_id == 6:
                print(f"🔧 区域6映射构建: '{processed_label}' → '{original_label}' → 键{lookup_key} → ID'{twin_id}'")
    
    # 检查区域6的映射
    print(f"\n📊 区域6的映射情况:")
    region_6_mappings = {k: v for k, v in previous_frame_mapping.items() if k[0] == 6}
    
    for key, cards in region_6_mappings.items():
        print(f"  映射键{key}: {len(cards)}张卡牌")
        for card in cards:
            print(f"    - {card['processed_label']} → {card['twin_id']}")
    
    # 特别检查"八"的映射
    ba_key = (6, '八')
    if ba_key in previous_frame_mapping:
        print(f"\n✅ 找到区域6的'八'映射: 键{ba_key}")
        for card in previous_frame_mapping[ba_key]:
            print(f"  - {card['processed_label']} → {card['twin_id']}")
    else:
        print(f"\n❌ 没有找到区域6的'八'映射: 键{ba_key}")
        print(f"可用的区域6映射键:")
        for key in region_6_mappings.keys():
            print(f"  - {key}")
    
    return previous_frame_mapping

def simulate_frame_124_lookup(previous_frame_mapping):
    """模拟Frame_00124的查找过程"""
    print(f"\n🔍 模拟Frame_00124的查找过程")
    print("=" * 80)
    
    # 模拟Frame_00124中区域6的"八"查找
    lookup_key = (6, '八')
    
    print(f"🔧 查找键: {lookup_key}")
    
    if lookup_key in previous_frame_mapping:
        cards = previous_frame_mapping[lookup_key]
        print(f"✅ 找到映射: {len(cards)}张卡牌")
        for card in cards:
            print(f"  - {card['processed_label']} → {card['twin_id']}")
        
        # 模拟继承逻辑
        if len(cards) == 1:
            print(f"✅ 应该继承: {cards[0]['twin_id']}")
        else:
            print(f"⚠️ 多张卡牌，需要空间排序匹配")
    else:
        print(f"❌ 没有找到映射")
        print(f"这会导致跨区域继承或作为新卡牌")

def main():
    """主函数"""
    print("🚀 Frame_00123映射构建调试")
    print("🎯 目标: 检查区域6的映射是否正确构建")
    print("=" * 80)
    
    # 步骤1: 调试映射构建
    previous_frame_mapping = debug_frame_123_mapping()
    
    # 步骤2: 模拟Frame_00124查找
    simulate_frame_124_lookup(previous_frame_mapping)
    
    print("\n🎯 调试结论:")
    print("=" * 80)
    print("如果找到了区域6的'八'映射，说明映射构建正确")
    print("如果没有找到，说明映射构建有问题")
    print("如果找到了但Frame_00124仍然有问题，说明查找逻辑有问题")

if __name__ == "__main__":
    main()
