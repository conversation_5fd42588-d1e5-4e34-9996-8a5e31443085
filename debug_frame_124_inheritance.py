#!/usr/bin/env python3
"""
Frame_00124继承错误分析脚本

分析Frame_00122 -> Frame_00123 -> Frame_00124的卡牌继承流程
重点关注"八"牌的继承问题
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    file_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_cards_by_region(frame_data: Dict[str, Any]) -> Dict[int, List[Dict[str, Any]]]:
    """按区域提取卡牌数据"""
    cards_by_region = {}
    
    for shape in frame_data.get('shapes', []):
        region_id = shape.get('group_id')
        if region_id not in cards_by_region:
            cards_by_region[region_id] = []
        
        card_info = {
            'label': shape.get('label'),
            'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
            'region_id': region_id,
            'region_name': shape.get('region_name'),
            'bbox': shape.get('points', []),
            'score': shape.get('score')
        }
        cards_by_region[region_id].append(card_info)
    
    return cards_by_region

def find_cards_with_label(cards_by_region: Dict[int, List[Dict[str, Any]]], target_label: str) -> List[Dict[str, Any]]:
    """查找包含特定标签的所有卡牌"""
    found_cards = []
    for region_id, cards in cards_by_region.items():
        for card in cards:
            if target_label in card['label']:
                found_cards.append(card)
    return found_cards

def analyze_inheritance_flow():
    """分析继承流程"""
    print("🔍 Frame_00124继承错误分析")
    print("=" * 60)
    
    # 加载三帧数据
    frame_122 = load_frame_data(122)
    frame_123 = load_frame_data(123)
    frame_124 = load_frame_data(124)
    
    if not all([frame_122, frame_123, frame_124]):
        print("❌ 无法加载所有帧数据")
        return
    
    # 按区域提取卡牌
    cards_122 = extract_cards_by_region(frame_122)
    cards_123 = extract_cards_by_region(frame_123)
    cards_124 = extract_cards_by_region(frame_124)
    
    print("\n📊 各帧'八'牌分布情况:")
    print("-" * 40)
    
    # 分析Frame_00122中的"八"牌
    print("Frame_00122中的'八'牌:")
    ba_cards_122 = find_cards_with_label(cards_122, '八')
    for card in ba_cards_122:
        print(f"  区域{card['region_id']} ({card['region_name']}): {card['label']} -> ID: {card['twin_id']}")
    
    # 分析Frame_00123中的"八"牌
    print("\nFrame_00123中的'八'牌:")
    ba_cards_123 = find_cards_with_label(cards_123, '八')
    for card in ba_cards_123:
        print(f"  区域{card['region_id']} ({card['region_name']}): {card['label']} -> ID: {card['twin_id']}")
    
    # 分析Frame_00124中的"八"牌
    print("\nFrame_00124中的'八'牌:")
    ba_cards_124 = find_cards_with_label(cards_124, '八')
    for card in ba_cards_124:
        print(f"  区域{card['region_id']} ({card['region_name']}): {card['label']} -> ID: {card['twin_id']}")
    
    print("\n🔄 继承流程分析:")
    print("-" * 40)
    
    # 分析Frame_00122 -> Frame_00123的继承
    print("Frame_00122 -> Frame_00123:")
    print("  Frame_00122手牌区(区域1)有: 1八, 2八")
    print("  Frame_00123手牌区(区域1)有: 1八")
    print("  Frame_00123吃碰区(区域6)有: 2八")
    print("  ✅ 分析: 2八从手牌区流转到吃碰区，1八保留在手牌区")
    
    # 分析Frame_00123 -> Frame_00124的继承
    print("\nFrame_00123 -> Frame_00124:")
    print("  Frame_00123手牌区(区域1)有: 1八")
    print("  Frame_00123吃碰区(区域6)有: 2八")
    print("  Frame_00124手牌区(区域1)有: 1八")
    print("  Frame_00124吃碰区(区域6)有: 1八")
    print("  ❌ 问题: 区域6的'1八'应该继承区域6的'2八'，而不是区域1的'1八'")
    
    print("\n🚨 问题根因分析:")
    print("-" * 40)
    print("1. Frame_00124中区域6的'1八'错误继承了区域1的'1八'的ID")
    print("2. 正确的继承应该是: 区域6的'1八' -> 继承区域6的'2八'的ID")
    print("3. 这表明继承逻辑在跨区域流转时存在问题")
    
    print("\n💡 可能的原因:")
    print("-" * 40)
    print("1. 继承优先级错误: 优先匹配了相同标签而非相同区域")
    print("2. 区域流转逻辑缺失: 没有正确处理区域内的卡牌继承")
    print("3. ID分配策略问题: 没有考虑区域内卡牌的连续性")

    print("\n🔍 深入分析继承逻辑问题:")
    print("-" * 40)

    # 分析Frame_00123 -> Frame_00124的具体继承问题
    print("Frame_00123 -> Frame_00124的继承分析:")
    print("  Frame_00123状态:")
    print("    - 区域1(手牌): 1八 (ID: 1八)")
    print("    - 区域6(吃碰): 2八 (ID: 2八)")
    print("  Frame_00124状态:")
    print("    - 区域1(手牌): 1八 (ID: 1八) ✅ 正确继承")
    print("    - 区域6(吃碰): 1八 (ID: 1八) ❌ 错误！应该是ID: 2八")

    print("\n🚨 核心问题:")
    print("  区域6的'1八'应该继承区域6的'2八'的ID，而不是区域1的'1八'的ID")
    print("  这说明SimpleInheritor在处理区域6时，没有正确执行区域内优先继承")

    print("\n🔧 预期的正确逻辑:")
    print("  1. 区域6优先级继承应该查找前一帧区域6的'八'牌")
    print("  2. 找到前一帧区域6的'2八'")
    print("  3. 当前帧区域6的'1八'应该继承'2八'的ID")
    print("  4. 结果应该是: 区域6的'1八' -> ID: 2八")
    
    # 详细分析区域6的情况
    print("\n🔍 区域6(吃碰区_观战方)详细分析:")
    print("-" * 40)
    
    region_6_122 = cards_122.get(6, [])
    region_6_123 = cards_123.get(6, [])
    region_6_124 = cards_124.get(6, [])
    
    print("Frame_00122区域6:")
    for card in region_6_122:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    print("Frame_00123区域6:")
    for card in region_6_123:
        print(f"  {card['label']} -> ID: {card['twin_id']}")
    
    print("Frame_00124区域6:")
    for card in region_6_124:
        print(f"  {card['label']} -> ID: {card['twin_id']}")

def test_inheritance_logic():
    """测试继承逻辑的具体实现"""
    print("\n🧪 测试继承逻辑实现:")
    print("-" * 40)

    # 模拟Frame_00123的前一帧映射
    previous_frame_mapping = {
        (1, '1八'): [{'twin_id': '1八', 'group_id': 1, 'label': '1八'}],
        (6, '2八'): [{'twin_id': '2八', 'group_id': 6, 'label': '2八'}]
    }

    # 模拟Frame_00124的当前卡牌
    current_cards = [
        {'twin_id': None, 'group_id': 1, 'label': '1八'},  # 区域1的1八
        {'twin_id': None, 'group_id': 6, 'label': '1八'}   # 区域6的1八
    ]

    print("前一帧映射:")
    for key, cards in previous_frame_mapping.items():
        region, label = key
        print(f"  区域{region}-'{label}': {[c['twin_id'] for c in cards]}")

    print("\n当前帧卡牌:")
    for card in current_cards:
        print(f"  区域{card['group_id']}-'{card['label']}'")

    print("\n🔧 区域6优先级继承逻辑测试:")
    print("  查找键: (6, '1八') -> 不存在于前一帧映射")
    print("  问题: 区域6没有'1八'的前一帧数据，但有'2八'的数据")
    print("  当前逻辑: 可能回退到跨区域继承，错误地从区域1继承")

    print("\n💡 修复方案:")
    print("  1. 区域6应该使用基础标签匹配: '1八' -> 基础标签'八'")
    print("  2. 查找区域6中所有'八'类卡牌: 找到'2八'")
    print("  3. 继承'2八'的ID，而不是跨区域继承")

if __name__ == "__main__":
    analyze_inheritance_flow()
    test_inheritance_logic()
