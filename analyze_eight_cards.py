import json

# 读取frame_00123数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00123.json', 'r', encoding='utf-8') as f:
    f123 = json.load(f)

# 读取frame_00124数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00124.json', 'r', encoding='utf-8') as f:
    f124 = json.load(f)

# 查找frame_00123中的"八"卡牌
cards_123 = [s for s in f123['shapes'] if '八' in s['label']]
print('Frame_00123 八 cards:')
for s in cards_123:
    print(f"  Group {s['group_id']}: {s['label']} ({s['attributes'].get('digital_twin_id', 'N/A')})")

# 查找frame_00124中的"八"卡牌
cards_124 = [s for s in f124['shapes'] if '八' in s['label']]
print('\nFrame_00124 八 cards:')
for s in cards_124:
    print(f"  Group {s['group_id']}: {s['label']} ({s['attributes'].get('digital_twin_id', 'N/A')})")