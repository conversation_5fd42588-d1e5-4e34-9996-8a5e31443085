#!/usr/bin/env python3
"""
修复Frame_00124的继承错误问题
专门针对重复ID"1八"的问题进行修复
"""

import json
import sys
from pathlib import Path
from collections import defaultdict

def analyze_frame_123_and_124():
    """分析Frame_00123和Frame_00124的继承关系"""
    print("🔍 分析Frame_00123和Frame_00124的继承关系")
    print("=" * 60)
    
    # 读取Frame_00123数据
    frame_123_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00123.json")
    frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
    
    if not frame_123_file.exists():
        print("❌ frame_00123.json不存在")
        return False
    
    if not frame_124_file.exists():
        print("❌ frame_00124.json不存在")
        return False
    
    with open(frame_123_file, 'r', encoding='utf-8') as f:
        frame_123_data = json.load(f)
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        frame_124_data = json.load(f)
    
    # 分析Frame_00123中的"八"
    print("📊 Frame_00123中的'八':")
    frame_123_ba_cards = []
    for shape in frame_123_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            frame_123_ba_cards.append({
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'points': shape.get('points', [])
            })
            print(f"  区域{shape.get('group_id')}: {shape.get('label')}")
    
    # 分析Frame_00124中的"八"
    print("\n📊 Frame_00124中的'八':")
    frame_124_ba_cards = []
    for shape in frame_124_data.get('shapes', []):
        if '八' in shape.get('label', ''):
            frame_124_ba_cards.append({
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'points': shape.get('points', [])
            })
            print(f"  区域{shape.get('group_id')}: {shape.get('label')}")
    
    # 分析继承关系
    print("\n🔗 继承关系分析:")
    print("Frame_00123 → Frame_00124 应该的继承关系:")
    print("  区域1的'八' → 区域1的'八' (继承1八)")
    print("  区域6的'八' → 区域6的'八' (应该继承为2八，因为1八已被区域1使用)")
    print("  区域2的'八' → 新出现，应该分配3八")
    
    return True

def test_digital_twin_processing():
    """测试数字孪生处理Frame_00124"""
    print("\n🧪 测试数字孪生处理Frame_00124")
    print("=" * 60)
    
    try:
        # 导入数字孪生系统
        sys.path.append('src')
        from modules import create_phase2_integrator
        
        # 创建处理器
        processor = create_phase2_integrator()
        
        # 读取Frame_00124数据
        frame_124_file = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
        with open(frame_124_file, 'r', encoding='utf-8') as f:
            frame_124_data = json.load(f)
        
        # 转换为检测格式
        detections = []
        for shape in frame_124_data.get('shapes', []):
            points = shape.get('points', [])

            # 将points转换为bbox格式 [x1, y1, x2, y2]
            if points and len(points) >= 2:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            else:
                bbox = [0, 0, 100, 100]  # 默认bbox

            detection = {
                'label': shape.get('label', ''),
                'group_id': shape.get('group_id'),
                'bbox': bbox,
                'confidence': shape.get('score', 1.0),
                'points': points,
                'score': shape.get('score', 1.0)
            }
            detections.append(detection)
        
        print(f"📥 输入: {len(detections)}张卡牌")
        
        # 处理
        result = processor.process_frame(detections)
        
        if result.success:
            print(f"✅ 处理成功: {len(result.processed_cards)}张卡牌")
            
            # 检查重复ID
            id_counts = defaultdict(int)
            ba_cards = []
            
            for card in result.processed_cards:
                twin_id = card.get('twin_id', 'N/A')
                if twin_id != 'N/A':
                    id_counts[twin_id] += 1
                
                if '八' in card.get('label', ''):
                    ba_cards.append({
                        'region': card.get('group_id'),
                        'label': card.get('label'),
                        'twin_id': twin_id
                    })
            
            # 检查重复ID
            duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
            
            print(f"\n📊 '八'卡牌处理结果:")
            for card in ba_cards:
                print(f"  区域{card['region']}: {card['label']} → {card['twin_id']}")
            
            if duplicate_ids:
                print(f"\n❌ 发现重复ID:")
                for twin_id, count in duplicate_ids.items():
                    print(f"  {twin_id}: 出现{count}次")
                return False
            else:
                print(f"\n✅ 无重复ID问题")
                return True
        else:
            print(f"❌ 处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_calibration_processor():
    """运行校准处理器重新生成Frame_00124"""
    print("\n🔄 运行校准处理器重新生成Frame_00124")
    print("=" * 60)
    
    try:
        # 运行校准处理器
        import subprocess
        result = subprocess.run([
            sys.executable, "calibration_gt_final_processor.py"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ 校准处理器运行成功")
            return True
        else:
            print(f"❌ 校准处理器运行失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 运行校准处理器失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Frame_00124继承错误修复")
    print("🎯 目标: 修复区域6的'八'继承错误，确保ID唯一性")
    print("=" * 80)
    
    # 步骤1: 分析继承关系
    if not analyze_frame_123_and_124():
        print("❌ 继承关系分析失败")
        return False
    
    # 步骤2: 测试数字孪生处理
    print("\n" + "="*80)
    if not test_digital_twin_processing():
        print("❌ 数字孪生处理测试失败")
        
        # 步骤3: 重新运行校准处理器
        print("\n" + "="*80)
        if run_calibration_processor():
            # 重新测试
            print("\n" + "="*80)
            print("🔄 重新测试数字孪生处理")
            if test_digital_twin_processing():
                print("\n🎉 Frame_00124修复成功！")
                return True
            else:
                print("\n❌ 重新处理后仍有问题")
                return False
        else:
            print("\n❌ 校准处理器运行失败")
            return False
    else:
        print("\n🎉 Frame_00124已经正确！")
        return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ Frame_00124修复验证通过")
    else:
        print(f"\n❌ Frame_00124修复失败")
    
    sys.exit(0 if success else 1)
