import json

# 读取frame_00123数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00123.json', 'r', encoding='utf-8') as f:
    f123 = json.load(f)

# 读取frame_00124数据
with open('D:/phz-ai-simple/output/calibration_gt_final_with_digital_twin/labels/frame_00124.json', 'r', encoding='utf-8') as f:
    f124 = json.load(f)

print("=== Frame_00123 中区域6的卡牌 ===")
region_6_cards_123 = [s for s in f123['shapes'] if s['group_id'] == 6]
for card in region_6_cards_123:
    print(f"标签: {card['label']}, ID: {card['attributes']['digital_twin_id']}")

print("\n=== Frame_00124 中区域6的卡牌 ===")
region_6_cards_124 = [s for s in f124['shapes'] if s['group_id'] == 6]
for card in region_6_cards_124:
    print(f"标签: {card['label']}, ID: {card['attributes']['digital_twin_id']}")

print("\n=== Frame_00123 中区域1的卡牌 ===")
region_1_cards_123 = [s for s in f123['shapes'] if s['group_id'] == 1]
for card in region_1_cards_123:
    print(f"标签: {card['label']}, ID: {card['attributes']['digital_twin_id']}")

print("\n=== Frame_00124 中区域1的卡牌 ===")
region_1_cards_124 = [s for s in f124['shapes'] if s['group_id'] == 1]
for card in region_1_cards_124:
    print(f"标签: {card['label']}, ID: {card['attributes']['digital_twin_id']}")

print("\n=== Frame_00124 中区域2的卡牌 ===")
region_2_cards_124 = [s for s in f124['shapes'] if s['group_id'] == 2]
for card in region_2_cards_124:
    print(f"标签: {card['label']}, ID: {card['attributes']['digital_twin_id']}")

# 分析继承问题
print("\n=== 继承问题分析 ===")
# 查找"八"相关的卡牌
eight_cards_123_region6 = [s for s in region_6_cards_123 if '八' in s['label']]
eight_cards_123_region1 = [s for s in region_1_cards_123 if '八' in s['label']]
eight_cards_124_region6 = [s for s in region_6_cards_124 if '八' in s['label']]
eight_cards_124_region1 = [s for s in region_1_cards_124 if '八' in s['label']]
eight_cards_124_region2 = [s for s in region_2_cards_124 if '八' in s['label']]

print(f"Frame_00123 区域6 '八'卡牌: {[c['attributes']['digital_twin_id'] for c in eight_cards_123_region6]}")
print(f"Frame_00123 区域1 '八'卡牌: {[c['attributes']['digital_twin_id'] for c in eight_cards_123_region1]}")
print(f"Frame_00124 区域6 '八'卡牌: {[c['attributes']['digital_twin_id'] for c in eight_cards_124_region6]}")
print(f"Frame_00124 区域1 '八'卡牌: {[c['attributes']['digital_twin_id'] for c in eight_cards_124_region1]}")
print(f"Frame_00124 区域2 '八'卡牌: {[c['attributes']['digital_twin_id'] for c in eight_cards_124_region2]}")

print("\n=== 问题描述 ===")
print("1. Frame_00123中区域6有'2八'卡牌")
print("2. Frame_00123中区域1有'1八'卡牌")
print("3. Frame_00124中区域6的'2八'卡牌变成了'1八'（错误继承）")
print("4. Frame_00124中区域1仍然有'1八'卡牌")
print("5. Frame_00124中区域2出现了新的'1八'卡牌")
print("\n应该优先从区域6继承'2八'，而不是从区域1继承'1八'")