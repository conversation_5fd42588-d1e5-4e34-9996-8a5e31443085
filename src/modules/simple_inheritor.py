"""
模块3：简单继承器 (SimpleInheritor)
基于区域状态的继承 - 符合GAME_RULES.md设计要求
"""

from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import logging
from .spatial_sorter import create_spatial_sorter

logger = logging.getLogger(__name__)

@dataclass
class InheritanceResult:
    """继承结果"""
    inherited_cards: List[Dict[str, Any]]
    new_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class SimpleInheritor:
    """基于区域状态的继承器 - 严格按照GAME_RULES.md设计要求"""

    def __init__(self):
        # 前一帧的卡牌映射：{(区域ID, 原始标签): [卡牌数据列表]}
        self.previous_frame_mapping: Dict[Tuple[int, str], List[Dict[str, Any]]] = {}
        # 空间排序器，用于继承匹配
        self.spatial_sorter = create_spatial_sorter()

        # 继承统计
        self.inheritance_stats = {
            "total_frames": 0,
            "total_cards_processed": 0,
            "total_inherited": 0,
            "total_new": 0,
            "region_state_matches": 0,  # 基于区域状态的匹配
            "exact_matches": 0
        }

        logger.info("基于区域状态的继承器初始化完成")
    
    def process_inheritance(self, current_cards: List[Dict[str, Any]]) -> InheritanceResult:
        """基于区域状态的继承逻辑 - 严格按照GAME_RULES.md设计要求"""
        print("🚨🚨🚨🚨🚨 [SIMPLE_INHERITOR_CALLED] SimpleInheritor.process_inheritance被调用了！🚨🚨🚨🚨🚨")
        logger.info("🚨🚨🚨🚨🚨 [SIMPLE_INHERITOR_CALLED] SimpleInheritor.process_inheritance被调用了！🚨🚨🚨🚨🚨")

        self.inheritance_stats["total_frames"] += 1
        self.inheritance_stats["total_cards_processed"] += len(current_cards)

        logger.info(f"开始处理基于区域状态的继承，当前帧{len(current_cards)}张卡牌，前一帧映射{len(self.previous_frame_mapping)}个")

        # 第一帧：没有前一帧数据，全部作为新卡牌
        if not self.previous_frame_mapping:
            logger.info("第一帧：所有卡牌作为新卡牌")
            return InheritanceResult(
                inherited_cards=[],
                new_cards=current_cards,
                statistics=self._generate_statistics()
            )

        # 后续帧：基于原始标签和内容替换进行继承
        inherited_cards = []
        new_cards = []

        # 🔧 添加区域4数据调试
        region_4_cards = [card for card in current_cards if card.get('group_id') == 4]
        print(f"🚨🚨🚨 [INPUT_DEBUG] SimpleInheritor接收到{len(current_cards)}张卡牌，其中区域4有{len(region_4_cards)}张 🚨🚨🚨")
        logger.info(f"🚨🚨🚨 [INPUT_DEBUG] SimpleInheritor接收到{len(current_cards)}张卡牌，其中区域4有{len(region_4_cards)}张 🚨🚨🚨")

        if region_4_cards:
            print(f"🚨🚨🚨 [REGION_4_INPUT] 发现区域4数据: {len(region_4_cards)}张卡牌 🚨🚨🚨")
            logger.info(f"🔧 [调试] SimpleInheritor接收到区域4数据: {len(region_4_cards)}张卡牌")
            for card in region_4_cards:
                label = card.get('label', 'None')
                print(f"🚨🚨🚨 [REGION_4_CARD] 区域4卡牌: 标签='{label}', group_id={card.get('group_id')} 🚨🚨🚨")
                logger.info(f"🔧 [调试] 区域4卡牌: 标签='{label}', group_id={card.get('group_id')}")
        else:
            print(f"🚨🚨🚨 [NO_REGION_4] SimpleInheritor未接收到区域4数据 🚨🚨🚨")
            logger.info(f"🔧 [调试] SimpleInheritor未接收到区域4数据")

        # 按原始标签分组当前帧卡牌
        from collections import defaultdict
        current_by_original = defaultdict(list)

        for current_card in current_cards:
            group_id = current_card.get('group_id')
            original_label = current_card.get('label')  # 当前帧的标签就是原始标签

            # 🔧 调试区域4的分组
            if group_id == 4:
                print(f"🚨🚨🚨 [GROUPING_DEBUG] 区域4卡牌分组: 标签'{original_label}' -> 键({group_id}, '{original_label}') 🚨🚨🚨")

            # 只有有效的区域ID和标签才进行继承查找
            if group_id is not None and original_label is not None:
                lookup_key = (group_id, original_label)
                current_by_original[lookup_key].append(current_card)

                # 🔧 调试区域4的分组结果
                if group_id == 4:
                    print(f"🚨🚨🚨 [GROUP_4_ADDED] 区域4卡牌已添加到组: 键{lookup_key} 🚨🚨🚨")
            else:
                # 无效的区域ID或标签，作为新卡牌
                new_cards.append(current_card)
                self.inheritance_stats["total_new"] += 1
                logger.warning(f"无效卡牌数据: 区域{group_id}, 标签{original_label}")

                # 🔧 调试区域4的无效数据
                if group_id == 4:
                    print(f"🚨🚨🚨 [GROUP_4_INVALID] 区域4卡牌数据无效: group_id={group_id}, label={original_label} 🚨🚨🚨")

        # 🔧 特殊处理：吃碰区域的整体继承逻辑（区域6和区域16）
        eating_regions = [6, 16]
        for region_id in eating_regions:
            if region_id in [key[0] for key in current_by_original.keys()]:
                region_result = self._process_eating_region_inheritance(region_id, current_by_original, inherited_cards, new_cards)
                if region_result:
                    # 移除已处理的区域数据
                    keys_to_remove = [key for key in current_by_original.keys() if key[0] == region_id]
                    for key in keys_to_remove:
                        del current_by_original[key]

        # 🔧 特殊处理：区域9的位置稳定性继承逻辑
        if 9 in [key[0] for key in current_by_original.keys()]:
            region_result = self._process_region9_stability_inheritance(current_by_original, inherited_cards, new_cards)
            if region_result:
                # 移除已处理的区域9数据
                keys_to_remove = [key for key in current_by_original.keys() if key[0] == 9]
                for key in keys_to_remove:
                    del current_by_original[key]

        # 🔧 优先处理区域6的内部继承（6→6），再处理其他区域
        region6_keys = [key for key in current_by_original.keys() if key[0] == 6]
        other_keys = [key for key in current_by_original.keys() if key[0] != 6]

        # 按优先级排序：区域6优先
        sorted_keys = region6_keys + other_keys

        # 🔧 调试分组结果
        print(f"🚨🚨🚨 [GROUPING_FINAL] 最终分组结果，共{len(current_by_original)}个组，处理顺序{len(sorted_keys)}个 🚨🚨🚨")
        for lookup_key in sorted_keys:
            group_id, original_label = lookup_key
            cards_count = len(current_by_original[lookup_key])
            if group_id == 4:
                print(f"🚨🚨🚨 [GROUP_4_IN_SORTED] 发现区域4在处理列表中: 键{lookup_key}, 卡牌数{cards_count} 🚨🚨🚨")
            else:
                print(f"[GROUP_INFO] 组: 键{lookup_key}, 卡牌数{cards_count}")

        # 对每个区域-标签组合进行继承处理
        for lookup_key in sorted_keys:
            current_cards_list = current_by_original[lookup_key]
            group_id, original_label = lookup_key

            # 🔧 添加超级明显的调试信息
            print(f"🚨🚨🚨 [SUPER_DEBUG] 处理区域{group_id}, 标签'{original_label}', 卡牌数{len(current_cards_list)} 🚨🚨🚨")
            logger.info(f"🚨🚨🚨 [SUPER_DEBUG] 处理区域{group_id}, 标签'{original_label}', 卡牌数{len(current_cards_list)} 🚨🚨🚨")

            if group_id == 6:
                logger.info(f"🔧 优先处理区域6内部继承: 区域{group_id}, 标签{original_label}, 当前帧{len(current_cards_list)}张")
            elif group_id == 4:
                print(f"🚨🚨🚨 [REGION_4_DETECTED] 检测到区域4！标签'{original_label}', 卡牌数{len(current_cards_list)} 🚨🚨🚨")
                logger.info(f"🚨🚨🚨 [REGION_4_DETECTED] 检测到区域4！标签'{original_label}', 卡牌数{len(current_cards_list)} 🚨🚨🚨")
                logger.info(f"🔧 处理区域4优先级继承: 区域{group_id}, 标签'{original_label}', 当前帧{len(current_cards_list)}张")
            else:
                logger.debug(f"处理继承: 区域{group_id}, 原始标签{original_label}, 当前帧{len(current_cards_list)}张")

            # 🔧 区域4优先级继承逻辑：新简化方案
            if group_id == 4:
                print(f"🚨🚨🚨 [REGION_4_PROCESSING] 开始执行区域4特殊处理逻辑 🚨🚨🚨")
                logger.info(f"🚨🚨🚨 [REGION_4_PROCESSING] 开始执行区域4特殊处理逻辑 🚨🚨🚨")
                region_4_success = self._process_region_4_priority_inheritance(
                    current_cards_list, original_label, inherited_cards, new_cards
                )
                print(f"🚨🚨🚨 [REGION_4_RESULT] 区域4处理结果: {region_4_success} 🚨🚨🚨")
                logger.info(f"🚨🚨🚨 [REGION_4_RESULT] 区域4处理结果: {region_4_success} 🚨🚨🚨")
                if region_4_success:
                    logger.info(f"🔧 区域4优先级继承成功，跳过后续逻辑")
                    continue  # 区域4已处理完成，跳过后续逻辑

            if lookup_key in self.previous_frame_mapping:
                # 找到前一帧的同类型卡牌
                previous_cards_list = self.previous_frame_mapping[lookup_key]

                logger.info(f"找到前一帧数据: 区域{group_id}, 标签{original_label}, 前一帧{len(previous_cards_list)}张")

                # 使用空间排序进行匹配：确保相同空间位置的卡牌继承对应的ID
                matched_pairs = self._match_cards_by_spatial_order(current_cards_list, previous_cards_list, group_id)

                # 处理匹配成功的卡牌
                for current_card, previous_card in matched_pairs:
                    inherited_card = self._inherit_card_content_replacement(current_card, previous_card)

                    # 🔧 检查继承是否被拒绝
                    if inherited_card.get('inheritance_rejected', False):
                        # 继承被拒绝，作为新卡牌处理
                        new_cards.append(inherited_card)
                        self.inheritance_stats["total_new"] += 1
                        rejection_reason = inherited_card.get('rejection_reason', 'unknown')
                        current_pos = self._get_card_position_info(current_card)
                        logger.info(f"继承拒绝转新卡牌: {rejection_reason} 位置{current_pos} (区域{group_id}, 标签{original_label})")
                    else:
                        # 继承成功
                        inherited_cards.append(inherited_card)
                        self.inheritance_stats["total_inherited"] += 1
                        self.inheritance_stats["region_state_matches"] += 1
                        inherited_id = inherited_card.get('twin_id', inherited_card.get('label', 'unknown'))
                        current_pos = self._get_card_position_info(current_card)
                        logger.info(f"空间匹配继承: {inherited_id} 位置{current_pos} (区域{group_id}, 标签{original_label})")

                # 处理当前帧中无法匹配的卡牌（数量超出前一帧）
                matched_current_cards = {id(pair[0]) for pair in matched_pairs}
                unmatched_current_cards = [card for card in current_cards_list if id(card) not in matched_current_cards]

                # 🔧 修复：对于无法区域内继承的卡牌，尝试跨区域继承
                if unmatched_current_cards:
                    logger.debug(f"区域{group_id}有{len(unmatched_current_cards)}张无法区域内继承的'{original_label}'卡牌，尝试跨区域继承")

                    # 尝试跨区域继承
                    cross_inherited_success = self._try_cross_region_inheritance(
                        unmatched_current_cards, original_label, inherited_cards, group_id
                    )

                    if cross_inherited_success:
                        logger.info(f"跨区域继承成功处理了区域{group_id}的部分'{original_label}'卡牌")
                    else:
                        # 跨区域继承也失败，作为新卡牌处理
                        for current_card in unmatched_current_cards:
                            new_cards.append(current_card)
                            self.inheritance_stats["total_new"] += 1
                            current_pos = self._get_card_position_info(current_card)
                            logger.debug(f"新卡牌: 区域{group_id}, 标签{original_label}, 位置{current_pos}（区域内和跨区域继承都失败）")
            else:
                # 没有找到前一帧数据，尝试特殊继承
                inherited_success = False

                # 如果当前是区域2，尝试从区域1继承
                if group_id == 2:
                    inherited_success = self._try_1_to_2_inheritance(
                        current_cards_list, original_label, inherited_cards
                    )

                # 🔧 尝试跨区域继承（处理7→9等特殊流转）
                if not inherited_success:
                    inherited_success = self._try_cross_region_inheritance(
                        current_cards_list, original_label, inherited_cards, group_id
                    )

                # 如果没有通过特殊继承，则作为新卡牌
                if not inherited_success:
                    new_cards.extend(current_cards_list)
                    self.inheritance_stats["total_new"] += len(current_cards_list)
                    logger.debug(f"新卡牌组: 区域{group_id}, 标签{original_label}, {len(current_cards_list)}张（前一帧无此类型）")

        # 🔧 调试SimpleInheritor的最终输出
        all_processed_cards = inherited_cards + new_cards
        region_4_output = [card for card in all_processed_cards if card.get('group_id') == 4]
        print(f"🚨🚨🚨 [SIMPLE_INHERITOR_OUTPUT] SimpleInheritor最终输出: 总共{len(all_processed_cards)}张，区域4有{len(region_4_output)}张 🚨🚨🚨")
        for i, card in enumerate(region_4_output):
            label = card.get('label', 'None')
            twin_id = card.get('twin_id', 'None')
            print(f"  区域4输出[{i}]: 标签'{label}', ID'{twin_id}'")

        # 生成统计信息
        statistics = self._generate_statistics()

        logger.info(f"区域状态继承完成: 继承{len(inherited_cards)}张, 新增{len(new_cards)}张")

        return InheritanceResult(
            inherited_cards=inherited_cards,
            new_cards=new_cards,
            statistics=statistics
        )

    # 删除基于IOU的匹配方法，改为基于区域状态的直接匹配
    # 在process_inheritance方法中已经实现了基于(区域ID, 标签)的直接查找

    def _inherit_card_content_replacement(self, current_card: Dict[str, Any], previous_card: Dict[str, Any]) -> Dict[str, Any]:
        """
        内容替换继承：保持ID不变，只更新位置和其他属性

        核心原则：
        - ID保持：twin_id、is_virtual等核心属性完全保持不变
        - 内容替换：当前帧的位置、置信度等信息替换前一帧的对应信息
        """
        # 🔧 调试继承过程
        current_label = current_card.get('label', 'None')
        previous_twin_id = previous_card.get('twin_id', 'None')
        current_group_id = current_card.get('group_id', 'None')
        previous_group_id = previous_card.get('group_id', 'None')

        print(f"🚨🚨🚨 [INHERIT_PROCESS] 开始内容替换继承 🚨🚨🚨")
        print(f"  当前卡牌: 标签'{current_label}', 区域{current_group_id}")
        print(f"  前一帧卡牌: ID'{previous_twin_id}', 区域{previous_group_id}")

        # 从前一帧卡牌开始，保持所有核心属性
        inherited_card = previous_card.copy()

        # 更新当前帧的新信息（位置、置信度等）
        inherited_card.update({
            'points': current_card.get('points'),
            'bbox': current_card.get('bbox'),
            'score': current_card.get('score'),
            'confidence': current_card.get('confidence'),
            'region_name': current_card.get('region_name'),
            'owner': current_card.get('owner'),
            'description': current_card.get('description'),
            'difficult': current_card.get('difficult'),
            'shape_type': current_card.get('shape_type'),
            'group_id': current_card.get('group_id'),  # 保持当前卡牌的区域ID
        })

        # 🔧 关键修复：检查标签与ID类型匹配性
        # current_label = current_card.get('label')  # 已在上面定义
        # previous_twin_id = previous_card.get('twin_id', '')  # 已在上面定义

        # 检查是否存在标签与ID类型不匹配的情况
        is_current_dark = (current_label == '暗')
        is_previous_id_dark = ('暗' in previous_twin_id)

        if is_current_dark != is_previous_id_dark:
            # 标签与ID类型不匹配，拒绝继承
            logger.warning(f"继承类型不匹配: 当前标签'{current_label}' vs 前一帧ID'{previous_twin_id}' - 拒绝继承")
            # 返回当前卡牌作为新卡牌，不进行继承
            new_card = current_card.copy()
            new_card['inheritance_rejected'] = True
            new_card['rejection_reason'] = f"标签类型不匹配: {current_label} vs {previous_twin_id}"
            return new_card

        # 保持原始检测标签（不是分配后的标签）
        inherited_card['label'] = current_label

        # 标记为继承卡牌
        inherited_card['inherited'] = True
        inherited_card['inheritance_method'] = 'content_replacement'
        inherited_card['previous_twin_id'] = previous_twin_id

        final_twin_id = inherited_card.get('twin_id', 'None')
        final_label = inherited_card.get('label', 'None')
        final_group_id = inherited_card.get('group_id', 'None')

        print(f"🚨🚨🚨 [INHERIT_COMPLETE] 内容替换继承完成 🚨🚨🚨")
        print(f"  最终结果: 标签'{final_label}', ID'{final_twin_id}', 区域{final_group_id}")

        logger.debug(f"内容替换继承完成: {inherited_card.get('twin_id')} <- {current_label}")

        return inherited_card
    
    def _update_previous_frame(self, current_cards: List[Dict[str, Any]]):
        """更新前一帧映射 - 基于原始标签分组存储，支持同类型多张卡牌"""
        self.previous_frame_mapping.clear()

        for card in current_cards:
            group_id = card.get('group_id')
            processed_label = card.get('label')

            # 只有有效的区域ID和标签才建立映射
            if group_id is not None and processed_label is not None:
                # 提取原始标签作为查找键
                original_label = self._extract_original_label(processed_label)
                lookup_key = (group_id, original_label)

                card_copy = card.copy()

                # 修复ID格式，确保前一帧数据中的ID格式正确
                if 'twin_id' in card_copy:
                    card_copy['twin_id'] = self._fix_id_format(card_copy['twin_id'])

                # 按原始标签分组存储，支持同类型多张卡牌
                if lookup_key not in self.previous_frame_mapping:
                    self.previous_frame_mapping[lookup_key] = []

                self.previous_frame_mapping[lookup_key].append(card_copy)

        # 统计信息
        total_cards = sum(len(cards) for cards in self.previous_frame_mapping.values())
        logger.debug(f"更新前一帧映射: {len(self.previous_frame_mapping)}个区域-标签组合，共{total_cards}张卡牌")

    def _process_eating_region_inheritance(self, region_id: int, current_by_original: Dict, inherited_cards: List, new_cards: List) -> bool:
        """
        处理吃碰区域的特殊继承逻辑（区域6和区域16）

        吃碰区域是偎牌/提牌/跑牌区域，需要整体考虑继承：
        1. 明牌应该继承对应的明牌ID
        2. 暗牌应该继承对应的暗牌ID
        3. 新出现的偎牌组合进行新分配
        """
        # 收集指定区域的所有当前帧卡牌
        region_current_cards = []
        for key, cards_list in current_by_original.items():
            if key[0] == region_id:  # 指定区域
                region_current_cards.extend(cards_list)

        if not region_current_cards:
            return False

        # 收集指定区域的所有前一帧卡牌
        region_previous_cards = []
        for key, cards_list in self.previous_frame_mapping.items():
            if key[0] == region_id:  # 指定区域
                region_previous_cards.extend(cards_list)

        # 🔧 区域6特殊处理：如果没有找到精确匹配，尝试基础标签匹配
        if not region_previous_cards and region_id == 6:
            logger.info(f"🔧 区域6精确匹配失败，尝试基础标签匹配查找前一帧数据")
            # 提取当前帧所有卡牌的基础标签
            current_base_labels = set()
            for card in region_current_cards:
                base_label = self._extract_base_label(card.get('label', ''))
                current_base_labels.add(base_label)

            # 查找区域6中匹配基础标签的前一帧卡牌
            for key, cards_list in self.previous_frame_mapping.items():
                prev_group_id, prev_label = key
                if prev_group_id == region_id:  # 区域6
                    prev_base_label = self._extract_base_label(prev_label)
                    if prev_base_label in current_base_labels:
                        region_previous_cards.extend(cards_list)
                        logger.info(f"🔧 区域6基础标签匹配: 找到前一帧'{prev_label}' -> 基础标签'{prev_base_label}'")

            if region_previous_cards:
                logger.info(f"🔧 区域6基础标签匹配成功: 找到{len(region_previous_cards)}张前一帧卡牌")

        if not region_previous_cards:
            # 🔧 区域6没有前一帧数据时，尝试跨区域继承（如1→6跑牌场景）
            if region_id == 6:
                cross_region_success = self._try_cross_region_inheritance_for_eating_region(
                    region_current_cards, inherited_cards, new_cards
                )
                if cross_region_success:
                    logger.info(f"区域{region_id}跨区域继承成功")
                    return True

            # 没有前一帧数据且无法跨区域继承，全部作为新卡牌
            new_cards.extend(region_current_cards)
            self.inheritance_stats["total_new"] += len(region_current_cards)
            logger.info(f"区域{region_id}无前一帧数据，{len(region_current_cards)}张卡牌作为新卡牌")
            return True

        logger.info(f"区域{region_id}整体继承处理: 当前帧{len(region_current_cards)}张, 前一帧{len(region_previous_cards)}张")

        # 🔧 区域6特殊处理：使用优先级继承逻辑
        if region_id == 6:
            logger.info(f"🔧 区域6使用优先级继承逻辑")
            # 按标签分组处理
            from collections import defaultdict
            cards_by_label = defaultdict(list)
            for card in region_current_cards:
                original_label = card.get('label', '')
                cards_by_label[original_label].append(card)

            # 对每种标签使用优先级继承
            for original_label, current_cards_list in cards_by_label.items():
                self._process_region_6_priority_inheritance(
                    current_cards_list, original_label, inherited_cards, new_cards
                )

            return True

        # 🔧 其他区域：基于位置列的分组继承
        matched_pairs = self._match_cards_by_column_groups(region_current_cards, region_previous_cards, region_id)

        # 处理匹配成功的卡牌
        for current_card, previous_card in matched_pairs:
            inherited_card = self._inherit_card_content_replacement(current_card, previous_card)

            # 检查继承是否被拒绝
            if inherited_card.get('inheritance_rejected', False):
                # 继承被拒绝，作为新卡牌处理
                new_cards.append(inherited_card)
                self.inheritance_stats["total_new"] += 1
                rejection_reason = inherited_card.get('rejection_reason', 'unknown')
                current_pos = self._get_card_position_info(current_card)
                logger.info(f"区域{region_id}继承拒绝转新卡牌: {rejection_reason} 位置{current_pos}")
            else:
                # 继承成功
                inherited_cards.append(inherited_card)
                self.inheritance_stats["total_inherited"] += 1
                self.inheritance_stats["region_state_matches"] += 1
                inherited_id = inherited_card.get('twin_id', inherited_card.get('label', 'unknown'))
                current_pos = self._get_card_position_info(current_card)
                logger.info(f"区域{region_id}整体继承: {inherited_id} 位置{current_pos}")

        # 处理当前帧中无法匹配的卡牌（数量超出前一帧）
        matched_current_cards = {id(pair[0]) for pair in matched_pairs}
        unmatched_current_cards = [card for card in region_current_cards if id(card) not in matched_current_cards]

        for current_card in unmatched_current_cards:
            new_cards.append(current_card)
            self.inheritance_stats["total_new"] += 1
            current_pos = self._get_card_position_info(current_card)
            logger.info(f"区域{region_id}新卡牌: 位置{current_pos}（超出前一帧数量）")

        logger.info(f"区域{region_id}整体继承完成: 继承{len(matched_pairs)}张, 新增{len(unmatched_current_cards)}张")
        return True

    def _try_cross_region_inheritance_for_eating_region(self, region_current_cards: List[Dict[str, Any]],
                                                       inherited_cards: List[Dict[str, Any]],
                                                       new_cards: List[Dict[str, Any]]) -> bool:
        """
        为吃碰区域尝试跨区域继承（专门处理1→6跑牌场景）

        Args:
            region_current_cards: 区域6的当前帧卡牌
            inherited_cards: 继承卡牌列表（输出）
            new_cards: 新卡牌列表（输出）

        Returns:
            bool: 是否成功进行了跨区域继承
        """
        # 按标签分组当前卡牌
        from collections import defaultdict
        cards_by_label = defaultdict(list)

        for card in region_current_cards:
            base_label = self._extract_base_label(card.get('label', ''))
            cards_by_label[base_label].append(card)

        total_inherited = 0

        # 对每种标签尝试跨区域继承
        for base_label, current_cards_list in cards_by_label.items():
            # 查找区域1中相同基础标签的卡牌
            available_source_cards = []
            for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
                prev_group_id, prev_label = lookup_key
                if prev_group_id == 1:  # 只从区域1继承
                    prev_base_label = self._extract_base_label(prev_label)
                    if prev_base_label == base_label:
                        available_source_cards.extend(prev_cards_list)

            if available_source_cards:
                # 🔧 修复：按ID倒序排序源卡牌，优先继承较大的ID（如2八优先于1八）
                sorted_source_cards = sorted(available_source_cards,
                                           key=lambda card: self._extract_id_number(card.get('twin_id', '')),
                                           reverse=True)

                # 按空间位置排序当前卡牌
                sorted_current_cards = self._sort_cards_by_spatial_order(current_cards_list, 6)

                # 🔧 修复：处理不同场景的跨区域继承
                if len(sorted_current_cards) == len(sorted_source_cards):
                    # 🎯 提牌场景：4张相同卡牌从区域1完全流转到区域6
                    logger.info(f"🎯 检测到提牌场景: {len(sorted_current_cards)}张'{base_label}'从区域1完全流转到区域6")

                    for i, current_card in enumerate(sorted_current_cards):
                        if i < len(sorted_source_cards):
                            source_card = sorted_source_cards[i]
                            inherited_card = self._inherit_card_content_replacement(current_card, source_card)
                            inherited_card['cross_region_inherited'] = True
                            inherited_card['source_region'] = 1
                            inherited_card['flow_type'] = 'ti_pai'  # 提牌标记
                            inherited_cards.append(inherited_card)
                            total_inherited += 1

                            self.inheritance_stats["total_inherited"] += 1
                            self.inheritance_stats["region_state_matches"] += 1

                            inherited_id = inherited_card.get('twin_id', 'unknown')
                            current_pos = self._get_card_position_info(current_card)
                            logger.info(f"🎯 提牌继承: {inherited_id} 位置{current_pos} (第{i+1}张 ← 区域1第{i+1}张)")

                elif len(sorted_current_cards) > len(sorted_source_cards):
                    # 🎯 跑牌场景：新卡牌在最上面，继承卡牌在下面
                    logger.info(f"🎯 检测到跑牌场景: {len(sorted_current_cards)}张当前卡牌 vs {len(sorted_source_cards)}张源卡牌")

                    for i, current_card in enumerate(sorted_current_cards):
                        if i == 0:
                            # 第1张（最上面）作为新卡牌
                            new_cards.append(current_card)
                            self.inheritance_stats["total_new"] += 1
                            current_pos = self._get_card_position_info(current_card)
                            logger.info(f"🎯 区域6新卡牌: 位置{current_pos} (第1张，新流转)")
                        else:
                            # 后面的张继承源卡牌
                            source_index = i - 1  # 源卡牌索引
                            if source_index < len(sorted_source_cards):
                                source_card = sorted_source_cards[source_index]
                                inherited_card = self._inherit_card_content_replacement(current_card, source_card)
                                inherited_card['cross_region_inherited'] = True
                                inherited_card['source_region'] = 1
                                inherited_card['flow_type'] = 'pao_pai'  # 跑牌标记
                                inherited_cards.append(inherited_card)
                                total_inherited += 1

                                self.inheritance_stats["total_inherited"] += 1
                                self.inheritance_stats["region_state_matches"] += 1

                                inherited_id = inherited_card.get('twin_id', 'unknown')
                                current_pos = self._get_card_position_info(current_card)
                                logger.info(f"🎯 跑牌继承: {inherited_id} 位置{current_pos} (第{i+1}张 ← 区域1第{source_index+1}张)")
                else:
                    # 🎯 偎牌场景：当前卡牌少于源卡牌（3张当前 vs 4张源）
                    logger.info(f"🎯 检测到偎牌场景: {len(sorted_current_cards)}张当前卡牌 vs {len(sorted_source_cards)}张源卡牌")

                    for i, current_card in enumerate(sorted_current_cards):
                        if i < len(sorted_source_cards):
                            source_card = sorted_source_cards[i]
                            inherited_card = self._inherit_card_content_replacement(current_card, source_card)
                            inherited_card['cross_region_inherited'] = True
                            inherited_card['source_region'] = 1
                            inherited_card['flow_type'] = 'wei_pai'  # 偎牌标记
                            inherited_cards.append(inherited_card)
                            total_inherited += 1

                            self.inheritance_stats["total_inherited"] += 1
                            self.inheritance_stats["region_state_matches"] += 1

                            inherited_id = inherited_card.get('twin_id', 'unknown')
                            current_pos = self._get_card_position_info(current_card)
                            logger.info(f"🎯 偎牌继承: {inherited_id} 位置{current_pos} (第{i+1}张 ← 区域1第{i+1}张)")
            else:
                # 没有找到源卡牌，全部作为新卡牌
                new_cards.extend(current_cards_list)
                self.inheritance_stats["total_new"] += len(current_cards_list)
                logger.info(f"🎯 区域6无继承源: {len(current_cards_list)}张'{base_label}'作为新卡牌")

        logger.info(f"🎯 区域6跨区域继承完成: 继承{total_inherited}张，新增{len(new_cards) - len([c for c in new_cards if c not in region_current_cards])}张")
        return total_inherited > 0

    def _match_cards_by_column_groups(self, current_cards: List[Dict[str, Any]],
                                    previous_cards: List[Dict[str, Any]],
                                    region_id: int) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """
        基于位置列的分组继承匹配

        将卡牌按空间位置分列，每列作为一个偎牌组合进行整体继承
        这样可以确保暗牌与同列的明牌正确关联
        """
        logger.info(f"开始基于位置列的分组继承: 当前帧{len(current_cards)}张, 前一帧{len(previous_cards)}张")

        # 将当前帧和前一帧的卡牌分别按列分组
        current_columns = self._group_cards_by_columns(current_cards)
        previous_columns = self._group_cards_by_columns(previous_cards)

        logger.info(f"当前帧分为{len(current_columns)}列, 前一帧分为{len(previous_columns)}列")

        matched_pairs = []

        # 按列进行匹配
        current_column_keys = sorted(current_columns.keys())
        previous_column_keys = sorted(previous_columns.keys())

        # 逐列匹配：前一帧的每列尝试与当前帧的对应列匹配
        for i, prev_col_key in enumerate(previous_column_keys):
            if i < len(current_column_keys):
                curr_col_key = current_column_keys[i]

                prev_column_cards = previous_columns[prev_col_key]
                curr_column_cards = current_columns[curr_col_key]

                logger.info(f"匹配第{i+1}列: 前一帧{len(prev_column_cards)}张 vs 当前帧{len(curr_column_cards)}张")

                # 列内使用空间排序进行一对一匹配
                column_matched_pairs = self._match_cards_by_spatial_order(curr_column_cards, prev_column_cards, region_id)
                matched_pairs.extend(column_matched_pairs)

                logger.info(f"第{i+1}列匹配完成: {len(column_matched_pairs)}对")

        logger.info(f"基于位置列的分组继承完成: 总共匹配{len(matched_pairs)}对")
        return matched_pairs

    def _group_cards_by_columns(self, cards: List[Dict[str, Any]]) -> Dict[float, List[Dict[str, Any]]]:
        """
        将卡牌按空间位置分列

        使用与spatial_sorter相同的列分组逻辑
        """
        from collections import defaultdict

        columns = defaultdict(list)
        tolerance = 100  # 列宽度容差，与spatial_sorter保持一致

        for card in cards:
            # 计算卡牌的X中心坐标
            x_center = self._get_card_x_center(card)

            # 寻找合适的列
            assigned = False
            for x_key in columns.keys():
                if abs(x_center - x_key) <= tolerance:
                    columns[x_key].append(card)
                    assigned = True
                    break

            if not assigned:
                # 创建新列
                columns[x_center].append(card)

        return columns

    def _get_card_x_center(self, card: Dict[str, Any]) -> float:
        """获取卡牌的X中心坐标"""
        if 'points' in card and card['points']:
            # 使用points格式
            points = card['points']
            x_coords = [p[0] for p in points]
            return (min(x_coords) + max(x_coords)) / 2
        elif 'bbox' in card:
            # 使用bbox格式
            x_left, y_top, x_right, y_bottom = card['bbox']
            return (x_left + x_right) / 2
        else:
            logger.warning(f"卡牌缺少位置信息: {card.get('label', 'unknown')}")
            return 0.0

    def _extract_original_label(self, processed_label: str) -> str:
        """
        提取原始检测标签

        将分配后的标签还原为原始检测标签：
        - "1一" -> "一"
        - "2二" -> "二"
        - "虚拟三" -> "三"
        - "3四暗" -> "四"
        """
        if not processed_label:
            return processed_label

        import re

        # 处理虚拟牌：虚拟三 -> 三
        if processed_label.startswith('虚拟'):
            virtual_match = re.match(r'虚拟(.+)', processed_label)
            if virtual_match:
                return virtual_match.group(1)

        # 处理暗牌：3四暗 -> 四
        if processed_label.endswith('暗'):
            dark_match = re.match(r'\d*(.+)暗', processed_label)
            if dark_match:
                return dark_match.group(1)

        # 处理普通牌：1一 -> 一, 2二 -> 二
        normal_match = re.match(r'\d*(.+)', processed_label)
        if normal_match:
            original = normal_match.group(1)
            # 确保提取的是有效的牌面标签
            valid_labels = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                           "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"]
            if original in valid_labels:
                return original

        # 如果无法提取，返回原标签
        return processed_label

    def _fix_id_format(self, twin_id: str) -> str:
        """修复ID格式，去除区域信息 - 根据设计文档的ID格式标准"""
        if not twin_id:
            return twin_id

        import re

        # 处理虚拟ID：虚拟_牌面_区域 -> 虚拟_牌面 或 虚拟牌面区域 -> 虚拟牌面
        if twin_id.startswith('虚拟'):
            # 匹配 虚拟_牌面_数字 格式
            match = re.match(r'虚拟_([^_]+)_\d+', twin_id)
            if match:
                card_label = match.group(1)
                return f"虚拟{card_label}"

            # 匹配 虚拟牌面数字 格式
            match = re.match(r'虚拟([^0-9]+)\d+', twin_id)
            if match:
                card_label = match.group(1)
                return f"虚拟{card_label}"

            # 已经是正确格式或其他格式
            return twin_id

        # 处理暗牌ID：数字牌面暗区域 -> 数字牌面暗
        if '暗' in twin_id:
            # 匹配 数字牌面暗数字 格式
            match = re.match(r'(\d+)([^暗]+)暗\d+', twin_id)
            if match:
                sequence = match.group(1)
                card_label = match.group(2)
                return f"{sequence}{card_label}暗"
            else:
                # 已经是正确格式或其他格式
                return twin_id

        # 处理物理ID：数字牌面区域 -> 数字牌面
        # 匹配 数字牌面数字 格式（最后的数字是区域ID）
        match = re.match(r'(\d+)([^0-9]+)\d+$', twin_id)
        if match:
            sequence = match.group(1)
            card_label = match.group(2)
            return f"{sequence}{card_label}"

        # 已经是正确格式
        return twin_id

    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        total_processed = self.inheritance_stats["total_cards_processed"]
        total_inherited = self.inheritance_stats["total_inherited"]

        inheritance_rate = total_inherited / total_processed if total_processed > 0 else 0
        region_state_match_rate = self.inheritance_stats["region_state_matches"] / total_inherited if total_inherited > 0 else 0

        return {
            "total_frames": self.inheritance_stats["total_frames"],
            "total_cards_processed": total_processed,
            "total_inherited": total_inherited,
            "total_new": self.inheritance_stats["total_new"],
            "inheritance_rate": inheritance_rate,
            "region_state_match_rate": region_state_match_rate,
            "region_state_matches": self.inheritance_stats["region_state_matches"],
            "exact_matches": self.inheritance_stats["exact_matches"],
            "previous_frame_mapping_count": len(self.previous_frame_mapping)
        }

    def reset_inheritance_history(self):
        """重置继承历史（用于新局开始）"""
        self.previous_frame_mapping.clear()
        self.inheritance_stats = {
            "total_frames": 0,
            "total_cards_processed": 0,
            "total_inherited": 0,
            "total_new": 0,
            "region_state_matches": 0,
            "exact_matches": 0
        }
        logger.info("继承历史已重置")

    def get_inheritance_rate(self) -> float:
        """获取当前继承率"""
        if self.inheritance_stats["total_cards_processed"] == 0:
            return 0.0
        return (
            self.inheritance_stats["total_inherited"] /
            self.inheritance_stats["total_cards_processed"]
        )

    def has_previous_frame(self) -> bool:
        """检查是否有前一帧数据"""
        return len(self.previous_frame_mapping) > 0

    def _match_cards_by_spatial_order(self, current_cards: List[Dict[str, Any]],
                                     previous_cards: List[Dict[str, Any]],
                                     region_id: int) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """
        基于ID稳定性匹配卡牌（修复位置变化导致的继承错误）

        核心原则：
        1. 优先基于ID匹配（相同ID的卡牌直接匹配）
        2. 对于无法ID匹配的，使用空间排序作为备选
        3. 确保位置变化不会影响已建立的继承关系

        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表
            region_id: 区域ID

        Returns:
            List[Tuple]: [(当前帧卡牌, 前一帧卡牌)] 匹配对列表
        """
        print(f"🚨🚨🚨 [SPATIAL_MATCH_START] 开始稳定性匹配: 当前帧{len(current_cards)}张, 前一帧{len(previous_cards)}张, 区域{region_id} 🚨🚨🚨")
        logger.debug(f"开始稳定性匹配: 当前帧{len(current_cards)}张, 前一帧{len(previous_cards)}张, 区域{region_id}")

        # 🔧 调试输入数据
        print(f"🚨🚨🚨 [CURRENT_CARDS] 当前帧卡牌: 🚨🚨🚨")
        for i, card in enumerate(current_cards):
            label = card.get('label', 'None')
            twin_id = card.get('twin_id', 'None')
            print(f"  当前[{i}]: 标签'{label}', ID'{twin_id}'")

        print(f"🚨🚨🚨 [PREVIOUS_CARDS] 前一帧卡牌: 🚨🚨🚨")
        for i, card in enumerate(previous_cards):
            label = card.get('label', 'None')
            twin_id = card.get('twin_id', 'None')
            print(f"  前一帧[{i}]: 标签'{label}', ID'{twin_id}'")

        # 🔧 区域9特殊处理：基于位置稳定性的继承锁定机制
        if region_id == 9 and len(current_cards) >= 1:
            return self._match_cards_by_position_stability(current_cards, previous_cards, region_id)

        matched_pairs = []
        used_current_indices = set()
        used_previous_indices = set()

        # 第一阶段：基于ID直接匹配（最稳定的匹配方式）
        print(f"🚨🚨🚨 [PHASE_1] 开始第一阶段：基于ID直接匹配 🚨🚨🚨")
        for i, current_card in enumerate(current_cards):
            current_id = current_card.get('twin_id') or current_card.get('digital_twin_id')
            current_label = current_card.get('label', 'None')
            print(f"🚨🚨🚨 [CURRENT_CHECK] 检查当前卡牌[{i}]: 标签'{current_label}', ID'{current_id}' 🚨🚨🚨")

            if not current_id:
                print(f"  -> 当前卡牌无ID，跳过")
                continue

            for j, previous_card in enumerate(previous_cards):
                if j in used_previous_indices:
                    continue

                previous_id = previous_card.get('twin_id') or previous_card.get('digital_twin_id')
                previous_label = previous_card.get('label', 'None')
                print(f"    比较前一帧[{j}]: 标签'{previous_label}', ID'{previous_id}'")

                if current_id == previous_id:
                    matched_pairs.append((current_card, previous_card))
                    used_current_indices.add(i)
                    used_previous_indices.add(j)

                    current_pos = self._get_card_position_info(current_card)
                    previous_pos = self._get_card_position_info(previous_card)
                    print(f"🚨🚨🚨 [ID_MATCH] ID匹配成功: {current_id} 当前@{current_pos} ← 前一帧@{previous_pos} 🚨🚨🚨")
                    logger.debug(f"ID匹配: {current_id} 当前@{current_pos} ← 前一帧@{previous_pos}")
                    break

        # 第二阶段：对剩余卡牌使用空间排序匹配
        remaining_current = [current_cards[i] for i in range(len(current_cards)) if i not in used_current_indices]
        remaining_previous = [previous_cards[j] for j in range(len(previous_cards)) if j not in used_previous_indices]

        if remaining_current and remaining_previous:
            logger.debug(f"空间排序匹配剩余卡牌: 当前{len(remaining_current)}张, 前一帧{len(remaining_previous)}张")

            # 对剩余卡牌进行空间排序
            previous_sorting_result = self.spatial_sorter.sort_cards_by_spatial_order(remaining_previous, region_id)
            previous_sorted = previous_sorting_result.sorted_cards

            current_sorting_result = self.spatial_sorter.sort_cards_by_spatial_order(remaining_current, region_id)
            current_sorted = current_sorting_result.sorted_cards

            # 按排序后的位置进行一对一匹配
            match_count = min(len(current_sorted), len(previous_sorted))
            for i in range(match_count):
                current_card = current_sorted[i]
                previous_card = previous_sorted[i]
                matched_pairs.append((current_card, previous_card))

                current_pos = self._get_card_position_info(current_card)
                previous_pos = self._get_card_position_info(previous_card)
                current_id = current_card.get('twin_id', 'no_id')
                previous_id = previous_card.get('twin_id', 'no_id')

                logger.debug(f"空间匹配{i+1}: 当前{current_id}@{current_pos} ← 前一帧{previous_id}@{previous_pos}")

        print(f"🚨🚨🚨 [SPATIAL_MATCH_COMPLETE] 稳定性匹配完成: {len(matched_pairs)}对匹配成功 🚨🚨🚨")
        for i, (current_card, previous_card) in enumerate(matched_pairs):
            current_label = current_card.get('label', 'None')
            current_id = current_card.get('twin_id', 'None')
            previous_label = previous_card.get('label', 'None')
            previous_id = previous_card.get('twin_id', 'None')
            print(f"  匹配对[{i}]: 当前'{current_label}'(ID:{current_id}) <- 前一帧'{previous_label}'(ID:{previous_id})")

        logger.info(f"稳定性匹配完成: {len(matched_pairs)}对匹配成功")
        return matched_pairs

    def _get_card_position_info(self, card: Dict[str, Any]) -> str:
        """获取卡牌位置信息用于调试"""
        if 'points' in card and card['points']:
            points = card['points']
            y_coords = [p[1] for p in points]
            x_coords = [p[0] for p in points]
            y_bottom = max(y_coords)
            x_center = (min(x_coords) + max(x_coords)) / 2
            return f"(x={x_center:.1f}, y_bottom={y_bottom:.1f})"
        return "(unknown)"

    def _match_cards_by_position_stability(self, current_cards: List[Dict[str, Any]],
                                          previous_cards: List[Dict[str, Any]],
                                          region_id: int) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """
        🔧 区域9特殊处理：基于位置稳定性的继承锁定机制

        核心原则：
        1. 位置稳定性检测：识别位置变化小的卡牌作为"稳定卡牌"（老卡牌）
        2. 稳定卡牌优先继承：锁定其继承关系，避免ID混乱
        3. 移动卡牌灵活继承：允许跨区域继承或新分配

        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表
            region_id: 区域ID（应该是9）

        Returns:
            List[Tuple]: [(当前帧卡牌, 前一帧卡牌)] 匹配对列表
        """
        STABILITY_THRESHOLD = 4.0  # 位置稳定性阈值（像素）

        logger.info(f"🔧 区域{region_id}位置稳定性匹配: 当前帧{len(current_cards)}张, 前一帧{len(previous_cards)}张")

        matched_pairs = []
        used_current_indices = set()
        used_previous_indices = set()

        # 第一步：位置稳定性分析
        stable_cards = []  # 位置稳定的卡牌（老卡牌）
        mobile_cards = []  # 位置变化大的卡牌（新卡牌）

        for i, current_card in enumerate(current_cards):
            min_distance = float('inf')
            closest_previous_card = None

            # 计算与所有前一帧卡牌的最小距离
            for previous_card in previous_cards:
                distance = self._calculate_card_position_distance(current_card, previous_card)
                if distance < min_distance:
                    min_distance = distance
                    closest_previous_card = previous_card

            current_pos = self._get_card_position_info(current_card)
            current_id = current_card.get('twin_id', 'no_id')

            if min_distance < STABILITY_THRESHOLD:
                stable_cards.append((i, current_card, min_distance, closest_previous_card))
                logger.debug(f"稳定卡牌: {current_id}@{current_pos}, 距离={min_distance:.1f}px")
            else:
                mobile_cards.append((i, current_card, min_distance, closest_previous_card))
                logger.debug(f"移动卡牌: {current_id}@{current_pos}, 距离={min_distance:.1f}px")

        logger.info(f"位置分析完成: {len(stable_cards)}张稳定卡牌, {len(mobile_cards)}张移动卡牌")

        # 第二步：稳定卡牌优先继承（最高优先级）
        for i, current_card, distance, closest_previous_card in stable_cards:
            if closest_previous_card is None:
                continue

            # 🔧 检查标签兼容性
            current_label = current_card.get('label', '')
            previous_id = closest_previous_card.get('twin_id', '')

            # 提取基础标签进行比较
            current_base_label = self._extract_base_label(current_label)
            previous_base_label = self._extract_base_label(previous_id)

            if current_base_label != previous_base_label:
                # 标签不匹配，跳过这个匹配
                logger.debug(f"稳定卡牌标签不匹配: {current_label} vs {previous_id}，跳过")
                continue

            # 检查这张前一帧卡牌是否已被使用
            previous_index = None
            for j, prev_card in enumerate(previous_cards):
                if prev_card is closest_previous_card and j not in used_previous_indices:
                    previous_index = j
                    break

            if previous_index is not None:
                matched_pairs.append((current_card, closest_previous_card))
                used_current_indices.add(i)
                used_previous_indices.add(previous_index)

                current_pos = self._get_card_position_info(current_card)
                previous_pos = self._get_card_position_info(closest_previous_card)
                current_id = current_card.get('twin_id', 'no_id')

                logger.info(f"✅ 稳定继承: {current_id}@{current_pos} ← {previous_id}@{previous_pos} (距离={distance:.1f}px)")

        # 第三步：移动卡牌继承剩余的前一帧卡牌（考虑标签匹配）
        remaining_previous = [previous_cards[j] for j in range(len(previous_cards)) if j not in used_previous_indices]

        for i, current_card, distance, _ in mobile_cards:
            if i in used_current_indices:
                continue

            current_label = current_card.get('label', '')
            current_base_label = self._extract_base_label(current_label)

            # 寻找标签匹配的前一帧卡牌
            best_match = None
            best_match_index = -1

            for idx, previous_card in enumerate(remaining_previous):
                previous_id = previous_card.get('twin_id', '')
                previous_base_label = self._extract_base_label(previous_id)

                if current_base_label == previous_base_label:
                    best_match = previous_card
                    best_match_index = idx
                    break

            if best_match is not None:
                # 找到标签匹配的前一帧卡牌
                matched_pairs.append((current_card, best_match))
                used_current_indices.add(i)
                remaining_previous.pop(best_match_index)

                current_pos = self._get_card_position_info(current_card)
                previous_pos = self._get_card_position_info(best_match)
                current_id = current_card.get('twin_id', 'no_id')
                previous_id = best_match.get('twin_id', 'no_id')

                logger.info(f"🔄 移动继承: {current_id}@{current_pos} ← {previous_id}@{previous_pos}")
            elif remaining_previous:
                # 没有标签匹配的，使用第一个可用的前一帧卡牌
                previous_card = remaining_previous.pop(0)
                matched_pairs.append((current_card, previous_card))
                used_current_indices.add(i)

                current_pos = self._get_card_position_info(current_card)
                previous_pos = self._get_card_position_info(previous_card)
                current_id = current_card.get('twin_id', 'no_id')
                previous_id = previous_card.get('twin_id', 'no_id')

                logger.info(f"🔄 移动继承(无标签匹配): {current_id}@{current_pos} ← {previous_id}@{previous_pos}")

        logger.info(f"🔧 区域{region_id}位置稳定性匹配完成: {len(matched_pairs)}对匹配成功")
        return matched_pairs

    def _process_region9_stability_inheritance(self, current_by_original: Dict, inherited_cards: List, new_cards: List) -> bool:
        """
        🔧 区域9特殊处理：基于位置稳定性的整体继承逻辑

        处理区域9中所有卡牌的继承，解决同区域多张相同基础标签卡牌的ID重复问题

        Args:
            current_by_original: 当前帧按(区域ID, 标签)分组的卡牌数据
            inherited_cards: 继承卡牌列表（输出）
            new_cards: 新卡牌列表（输出）

        Returns:
            bool: 是否成功处理了区域9
        """
        region_id = 9

        # 收集区域9的所有当前帧卡牌
        region9_current_cards = []
        region9_keys = []
        for key, cards_list in current_by_original.items():
            if key[0] == region_id:
                region9_current_cards.extend(cards_list)
                region9_keys.append(key)

        if not region9_current_cards:
            return False

        # 收集区域9的所有前一帧卡牌
        region9_previous_cards = []
        for key, cards_list in self.previous_frame_mapping.items():
            if key[0] == region_id:
                region9_previous_cards.extend(cards_list)

        logger.info(f"🔧 区域9位置稳定性整体继承: 当前帧{len(region9_current_cards)}张, 前一帧{len(region9_previous_cards)}张")

        if not region9_previous_cards:
            # 没有前一帧数据，尝试跨区域继承
            for current_card in region9_current_cards:
                original_label = current_card.get('label', '')
                base_label = self._extract_base_label(original_label)

                # 尝试跨区域继承
                cross_inherited = self._try_cross_region_inheritance(
                    [current_card], original_label, inherited_cards, region_id
                )

                if not cross_inherited:
                    # 跨区域继承失败，作为新卡牌
                    new_cards.append(current_card)
                    self.inheritance_stats["total_new"] += 1

            logger.info(f"区域9无前一帧数据，处理完成")
            return True

        # 使用位置稳定性匹配
        matched_pairs = self._match_cards_by_position_stability(
            region9_current_cards, region9_previous_cards, region_id
        )

        # 处理匹配成功的卡牌
        matched_current_cards = set()
        for current_card, previous_card in matched_pairs:
            inherited_card = self._inherit_card_content_replacement(current_card, previous_card)

            # 检查继承是否被拒绝
            if inherited_card.get('inheritance_rejected', False):
                new_cards.append(current_card)
                self.inheritance_stats["total_new"] += 1
                logger.info(f"区域9继承拒绝转新卡牌: {inherited_card.get('rejection_reason', 'unknown')} 位置{self._get_card_position_info(current_card)}")
            else:
                inherited_cards.append(inherited_card)
                self.inheritance_stats["total_inherited"] += 1
                inherited_id = inherited_card.get('twin_id', 'unknown')
                current_pos = self._get_card_position_info(current_card)
                logger.info(f"区域9位置稳定性继承: {inherited_id} 位置{current_pos}")

            matched_current_cards.add(id(current_card))

        # 处理未匹配的当前帧卡牌
        unmatched_current_cards = [card for card in region9_current_cards if id(card) not in matched_current_cards]

        if unmatched_current_cards:
            logger.debug(f"区域9有{len(unmatched_current_cards)}张无法位置稳定性继承的卡牌，尝试跨区域继承")

            for current_card in unmatched_current_cards:
                original_label = current_card.get('label', '')

                # 尝试跨区域继承
                cross_inherited = self._try_cross_region_inheritance(
                    [current_card], original_label, inherited_cards, region_id
                )

                if not cross_inherited:
                    # 跨区域继承失败，作为新卡牌
                    new_cards.append(current_card)
                    self.inheritance_stats["total_new"] += 1
                    current_pos = self._get_card_position_info(current_card)
                    logger.debug(f"区域9新卡牌: 标签{original_label}, 位置{current_pos}（位置稳定性和跨区域继承都失败）")

        logger.info(f"区域9位置稳定性整体继承完成: 继承{len(matched_pairs)}张, 新增{len(unmatched_current_cards)}张")
        return True

    def _calculate_card_position_distance(self, card1: Dict[str, Any], card2: Dict[str, Any]) -> float:
        """
        计算两张卡牌之间的位置距离

        Args:
            card1: 第一张卡牌
            card2: 第二张卡牌

        Returns:
            float: 两张卡牌中心点之间的欧几里得距离
        """
        center1 = self._get_card_center(card1)
        center2 = self._get_card_center(card2)

        if center1 is None or center2 is None:
            return float('inf')

        # 计算欧几里得距离
        distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
        return distance

    def _get_card_center(self, card: Dict[str, Any]) -> Optional[Tuple[float, float]]:
        """
        获取卡牌的中心坐标

        Args:
            card: 卡牌数据

        Returns:
            Optional[Tuple[float, float]]: (x, y) 中心坐标，如果无法获取则返回None
        """
        if 'points' in card and card['points']:
            # 使用points格式
            points = card['points']
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            center_x = (min(x_coords) + max(x_coords)) / 2
            center_y = (min(y_coords) + max(y_coords)) / 2
            return (center_x, center_y)
        elif 'bbox' in card and len(card['bbox']) >= 4:
            # 使用bbox格式
            x_left, y_top, x_right, y_bottom = card['bbox']
            center_x = (x_left + x_right) / 2
            center_y = (y_top + y_bottom) / 2
            return (center_x, center_y)
        else:
            logger.warning(f"卡牌缺少位置信息: {card.get('label', 'unknown')}")
            return None

    def _try_1_to_2_inheritance(self, current_cards_list: List[Dict[str, Any]],
                               original_label: str,
                               inherited_cards: List[Dict[str, Any]]) -> bool:
        """
        尝试1-2转换继承：如果group_id 2匹配前帧group_id 1的标签，继承最大ID

        Args:
            current_cards_list: 当前区域2的卡牌列表
            original_label: 原始标签
            inherited_cards: 继承卡牌列表（输出）

        Returns:
            bool: 是否成功进行了1-2转换继承
        """
        # 提取基础标签（去除数字前缀）
        base_label = self._extract_base_label(original_label)

        # 查找前一帧区域1中相同基础标签的卡牌
        region1_matches = []
        for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
            prev_group_id, prev_label = lookup_key
            if prev_group_id == 1:  # 只查找区域1
                prev_base_label = self._extract_base_label(prev_label)
                if prev_base_label == base_label:
                    region1_matches.extend(prev_cards_list)

        if not region1_matches:
            logger.debug(f"1-2转换: 区域1中没有找到基础标签'{base_label}'的匹配卡牌")
            return False

        # 找到最大ID的卡牌
        max_id_card = self._find_max_id_card(region1_matches)
        if not max_id_card:
            logger.debug(f"1-2转换: 区域1中没有有效ID的'{base_label}'卡牌")
            return False

        # 为当前区域2的卡牌继承最大ID
        inherited_count = 0
        for current_card in current_cards_list:
            inherited_card = self._inherit_card_content_replacement(current_card, max_id_card)
            inherited_cards.append(inherited_card)
            inherited_count += 1

            self.inheritance_stats["total_inherited"] += 1
            self.inheritance_stats["region_state_matches"] += 1

            inherited_id = inherited_card.get('twin_id', 'unknown')
            current_pos = self._get_card_position_info(current_card)
            logger.info(f"1-2转换继承: {inherited_id} 位置{current_pos} (区域2, 标签{original_label} <- 区域1, 基础标签{base_label})")

        logger.info(f"1-2转换继承完成: {inherited_count}张区域2卡牌继承了区域1的'{base_label}'最大ID")
        return True

    def _extract_base_label(self, label: str) -> str:
        """
        提取基础标签（去除数字孪生ID前缀）

        Args:
            label: 原始标签，如 "1伍", "2伍", "伍"

        Returns:
            str: 基础标签，如 "伍"
        """
        if not label:
            return ""

        # 如果标签以数字开头，去除数字前缀
        if len(label) > 1 and label[0].isdigit():
            return label[1:]

        return label

    def _find_max_id_card(self, cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """找到ID最大的卡牌"""
        valid_cards = [card for card in cards if card.get('twin_id')]

        if not valid_cards:
            return None

        # 使用统一的ID提取方法
        max_card = max(valid_cards, key=lambda card: self._extract_id_number(card.get('twin_id', '')))
        return max_card

    def _try_cross_region_inheritance(self, current_cards_list: List[Dict[str, Any]],
                                     original_label: str,
                                     inherited_cards: List[Dict[str, Any]],
                                     current_region: int) -> bool:
        """
        🔧 尝试跨区域继承：处理7→9等特殊流转场景

        Args:
            current_cards_list: 当前区域的卡牌列表
            original_label: 原始标签
            inherited_cards: 继承卡牌列表（输出）
            current_region: 当前区域ID

        Returns:
            bool: 是否成功进行了跨区域继承
        """
        # 定义跨区域继承规则 - 🔧 根据GAME_RULES.md规范重构
        cross_region_rules = {
            # 观战方继承规则
            1: [3],                 # 手牌区可以从抓牌区继承  # 🔧 修复：1←3 (抓牌流转到手牌)
            2: [1],                 # 调整区可以从手牌区继承  # 🔧 2←1
            4: [2, 1],              # 打牌区优先从调整区继承，其次从手牌区继承  # 🔧 4←2, 4←1
            5: [3, 4, 2],           # 弃牌区优先从抓牌区、打牌区继承，其次调整区  # 🔧 5←3, 5←4, 5←2 (删除1区域)
            6: [1, 3, 4, 7, 8],     # 观战方吃碰区可以从手牌区、抓牌区、打牌区、对战方抓牌区、打牌区继承  # 🔧 6←1, 6←3, 6←4, 6←7, 6←8

            # 对战方继承规则
            9: [7, 8],              # 弃牌区可以从抓牌区、打牌区继承  # 🔧 9←7, 9←8
            16: [3, 4, 7, 8],       # 对战方吃碰区可以从观战方抓牌区、打牌区、对战方抓牌区、打牌区继承  # 🔧 16←3, 16←4, 16←7, 16←8

            # 最终区域继承规则
            11: [7, 8, 9, 10],      # 最终弃牌区可以从对战方各区域继承
            14: [1, 6, 16, 17],     # 赢方区域14可以从手牌区、吃碰区、区域17继承  # 🔧 14←1, 14←6, 14←16, 14←17
            15: [1, 6, 16, 17],     # 赢方区域15可以从手牌区、吃碰区、区域17继承  # 🔧 15←1, 15←6, 15←16, 15←17
        }

        if current_region not in cross_region_rules:
            logger.debug(f"跨区域继承: 区域{current_region}不在跨区域继承规则中")
            return False

        # 🔧 修复：统一使用_extract_original_label方法，确保与映射构建时的逻辑一致
        base_label = self._extract_original_label(original_label)

        logger.debug(f"跨区域继承开始: 区域{current_region}, 原始标签'{original_label}', 基础标签'{base_label}'")

        # 查找可能的源区域中相同基础标签的卡牌
        source_regions = cross_region_rules[current_region]
        cross_region_matches = []

        logger.debug(f"跨区域继承: 查找源区域{source_regions}")
        logger.debug(f"跨区域继承: 当前映射键: {list(self.previous_frame_mapping.keys())}")

        for source_region in source_regions:
            for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
                prev_group_id, prev_label = lookup_key
                if prev_group_id == source_region:
                    # 🔧 修复：使用相同的标签提取方法
                    prev_base_label = self._extract_original_label(prev_label)
                    logger.debug(f"跨区域继承: 检查源区域{source_region}, 前一帧标签'{prev_label}', 基础标签'{prev_base_label}'")
                    if prev_base_label == base_label:
                        cross_region_matches.extend(prev_cards_list)
                        logger.debug(f"跨区域继承: 找到匹配! 源区域{source_region}, 标签'{prev_label}' -> '{prev_base_label}', 卡牌数{len(prev_cards_list)}")

        if not cross_region_matches:
            logger.debug(f"跨区域继承: 在源区域{source_regions}中没有找到基础标签'{base_label}'的匹配卡牌")
            return False

        logger.debug(f"跨区域继承: 找到{len(cross_region_matches)}张匹配的源卡牌")
        for i, match in enumerate(cross_region_matches):
            twin_id = match.get('twin_id', 'None')
            group_id = match.get('group_id', 'None')
            logger.debug(f"  源卡牌{i+1}: ID='{twin_id}', 区域={group_id}")

        # 找到最近使用的卡牌（优先选择ID最大的，表示最新分配的）
        latest_card = self._find_latest_card(cross_region_matches)
        if not latest_card:
            logger.debug(f"跨区域继承: 没有找到有效ID的'{base_label}'卡牌")
            return False

        # 🔧 改进继承逻辑：按空间顺序分别继承可用的ID，而不是都继承同一个
        inherited_count = 0

        # 🔧 修复：检测消失的卡牌，优先继承消失的ID
        # 收集前一帧所有源区域的相同标签卡牌
        prev_source_region_cards = []
        for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
            prev_group_id, prev_label = lookup_key
            if prev_group_id in source_regions:  # 检查所有源区域
                prev_base_label = self._extract_original_label(prev_label)
                if prev_base_label == base_label:
                    prev_source_region_cards.extend(prev_cards_list)

        # 收集当前帧所有源区域的相同标签卡牌
        current_source_region_cards = []
        for source_region in source_regions:
            for lookup_key, current_cards_in_mapping in self.previous_frame_mapping.items():
                # 注意：这里需要从当前帧数据中查找，而不是从previous_frame_mapping
                pass

        # 从当前帧的所有卡牌中查找源区域的卡牌
        all_current_cards = current_cards_list  # 这里应该是所有当前帧卡牌，但我们只有当前区域的
        current_source_region_cards = []
        # 由于我们只有当前区域的卡牌，我们需要通过其他方式检测消失

        # 🔧 关键修复：找出从源区域消失的卡牌ID
        disappeared_cards = []
        for prev_card in prev_source_region_cards:
            prev_id = prev_card.get('twin_id', '')
            prev_region = prev_card.get('group_id', '')

            # 检查这个ID是否在当前帧的同一源区域中还存在
            # 由于我们无法直接访问当前帧的其他区域数据，我们假设如果ID不在当前处理的区域中
            # 且来自源区域，则可能已消失（这是一个简化的检测方法）
            if prev_region in source_regions:
                # 简化检测：如果前一帧源区域有这个ID，我们认为它可能消失了
                # 更准确的检测需要访问当前帧的完整数据
                disappeared_cards.append(prev_card)

        # 收集所有可用的源卡牌，优先使用消失的卡牌
        available_source_cards = []

        # 首先添加消失的卡牌（按ID倒序）
        disappeared_cards.sort(key=lambda card: self._extract_id_number(card.get('twin_id', '')), reverse=True)
        available_source_cards.extend(disappeared_cards)

        # 然后添加其他源区域的卡牌
        for source_region in source_regions:
            for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
                prev_group_id, prev_label = lookup_key
                if prev_group_id == source_region:
                    prev_base_label = self._extract_original_label(prev_label)
                    if prev_base_label == base_label:
                        # 排除已经在消失卡牌中的卡牌
                        for card in prev_cards_list:
                            if card not in disappeared_cards:
                                available_source_cards.append(card)

        # 🔧 最终排序：消失的卡牌已经在前面，其余按ID倒序
        if len(available_source_cards) > len(disappeared_cards):
            # 对非消失的卡牌部分进行排序
            non_disappeared = available_source_cards[len(disappeared_cards):]
            non_disappeared.sort(key=lambda card: self._extract_id_number(card.get('twin_id', '')), reverse=True)
            available_source_cards = disappeared_cards + non_disappeared

        # 🔧 新增：记录消失的卡牌信息
        if disappeared_cards:
            logger.info(f"🔧 检测到{len(disappeared_cards)}张消失的'{base_label}'卡牌:")
            for card in disappeared_cards:
                twin_id = card.get('twin_id', 'None')
                logger.info(f"  消失卡牌: ID='{twin_id}'")

        logger.debug(f"跨区域继承: 排序后的源卡牌:")
        for i, card in enumerate(available_source_cards):
            twin_id = card.get('twin_id', 'None')
            is_disappeared = card in disappeared_cards
            status = "消失" if is_disappeared else "存在"
            logger.debug(f"  排序源卡牌{i+1}: ID='{twin_id}' ({status})")

        # 按空间位置排序当前卡牌
        sorted_current_cards = self._sort_cards_by_spatial_order(current_cards_list, current_region)

        # 🔧 区域6特殊处理：确保从区域1流转时选择最大数值的卡牌
        if current_region == 6:
            logger.info(f"🔧 区域6特殊处理: 优先选择大数值卡牌进行继承")

            # 检查源卡牌是否主要来自区域1
            region1_source_cards = [card for card in available_source_cards if card.get('group_id') == 1]
            if region1_source_cards:
                logger.info(f"🔧 检测到区域1→6流转: {len(region1_source_cards)}张源卡牌")

                # 对于区域1的源卡牌，确保按ID倒序排序（大数值优先）
                region1_source_cards.sort(key=lambda card: self._extract_id_number(card.get('twin_id', '')), reverse=True)

                # 记录排序结果
                logger.info(f"🔧 区域1源卡牌排序结果（大数值优先）:")
                for i, card in enumerate(region1_source_cards):
                    twin_id = card.get('twin_id', 'None')
                    id_num = self._extract_id_number(twin_id)
                    logger.info(f"  优先级{i+1}: ID='{twin_id}' (数值: {id_num})")

                # 使用区域1的排序结果替换available_source_cards的前面部分
                other_source_cards = [card for card in available_source_cards if card.get('group_id') != 1]
                available_source_cards = region1_source_cards + other_source_cards

        # 逐一继承：第1张继承最优先的源卡牌
        for i, current_card in enumerate(sorted_current_cards):
            if i < len(available_source_cards):
                # 有可用的源卡牌，继承其ID
                source_card = available_source_cards[i]
                inherited_card = self._inherit_card_content_replacement(current_card, source_card)
                inherited_card['cross_region_inherited'] = True
                inherited_card['source_region'] = source_card.get('group_id')
                inherited_cards.append(inherited_card)
                inherited_count += 1

                inherited_id = inherited_card.get('twin_id', 'unknown')
                current_pos = self._get_card_position_info(current_card)
                source_region = source_card.get('group_id', 'unknown')

                if current_region == 6:
                    logger.info(f"🔧 区域6跨区域继承成功: {inherited_id} 位置{current_pos} (区域{current_region}第{i+1}张 ← 区域{source_region})")
                else:
                    logger.info(f"跨区域继承成功: {inherited_id} 位置{current_pos} (区域{current_region}第{i+1}张 ← 区域{source_region})")
            else:
                # 没有更多源卡牌，这张作为新卡牌处理
                logger.debug(f"跨区域继承: 新卡牌 位置{self._get_card_position_info(current_card)} (区域{current_region}第{i+1}张，无可继承源)")

        logger.info(f"跨区域继承完成: 继承{inherited_count}张，剩余{len(sorted_current_cards) - inherited_count}张作为新卡牌")
        return inherited_count > 0

    def _extract_id_number(self, twin_id: str) -> int:
        """从数字孪生ID中提取数字（如"2一"→2）"""
        if not twin_id:
            return 0

        # 提取开头的数字
        for i, char in enumerate(twin_id):
            if not char.isdigit():
                if i > 0:
                    return int(twin_id[:i])
                break
        return 0

    def _sort_cards_by_spatial_order(self, cards: List[Dict[str, Any]], region_id: int) -> List[Dict[str, Any]]:
        """按空间位置排序卡牌（从上到下，从左到右）"""
        def get_sort_key(card):
            bbox = card.get('bbox', [])
            if len(bbox) >= 4:
                # 使用bbox的中心点进行排序
                center_x = (bbox[0] + bbox[2]) / 2
                center_y = (bbox[1] + bbox[3]) / 2
                return (center_y, center_x)  # 先按Y轴（上下），再按X轴（左右）
            return (0, 0)

        return sorted(cards, key=get_sort_key)

    def _find_latest_card(self, cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """找到最新的卡牌（ID最大或最近处理的）"""
        valid_cards = [card for card in cards if card.get('twin_id')]

        if not valid_cards:
            return None

        # 使用统一的ID提取方法
        latest_card = max(valid_cards, key=lambda card: self._extract_id_number(card.get('twin_id', '')))
        return latest_card

    def _find_region_cards_by_base_label(self, region_id: int, base_label: str) -> List[Dict[str, Any]]:
        """
        查找指定区域中匹配基础标签的所有卡牌

        Args:
            region_id: 区域ID
            base_label: 基础标签（如'八'）

        Returns:
            List[Dict]: 匹配的卡牌列表，按ID数值排序（大数值优先）
        """
        matching_cards = []

        # 遍历前一帧映射，查找指定区域的卡牌
        for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
            prev_group_id, prev_label = lookup_key
            if prev_group_id == region_id:
                # 提取前一帧卡牌的基础标签
                prev_base_label = self._extract_base_label(prev_label)
                if prev_base_label == base_label:
                    matching_cards.extend(prev_cards_list)

        # 按ID数值排序，大数值优先
        if matching_cards:
            matching_cards.sort(key=lambda card: self._extract_id_number(card.get('twin_id', '')), reverse=True)

        return matching_cards

    def _process_region_4_priority_inheritance(self, current_cards_list: List[Dict[str, Any]],
                                             original_label: str,
                                             inherited_cards: List[Dict[str, Any]],
                                             new_cards: List[Dict[str, Any]]) -> bool:
        """
        🔧 区域4优先级继承逻辑：新简化方案

        优先级顺序：
        1. 优先级1: 本区域状态继承（4区域 → 4区域）
        2. 优先级2: 从2区域继承（2区域 → 4区域）
        3. 优先级3: 从1区域继承（1区域 → 4区域），如果1区域有多张同类别，选数值最大的

        Args:
            current_cards_list: 当前4区域的卡牌列表
            original_label: 原始标签
            inherited_cards: 继承卡牌列表（输出）
            new_cards: 新卡牌列表（输出）

        Returns:
            bool: 是否成功处理了4区域继承
        """
        print(f"🚨🚨🚨 [REGION_4_METHOD] 进入_process_region_4_priority_inheritance方法 🚨🚨🚨")
        print(f"🔧 开始区域4优先级继承: 标签'{original_label}', 卡牌数{len(current_cards_list)}")
        logger.info(f"🚨🚨🚨 [REGION_4_METHOD] 进入_process_region_4_priority_inheritance方法 🚨🚨🚨")
        logger.info(f"🔧 开始区域4优先级继承: 标签'{original_label}', 卡牌数{len(current_cards_list)}")

        # 🔧 调试前一帧映射
        print(f"🚨🚨🚨 [PREVIOUS_MAPPING] 前一帧映射总数: {len(self.previous_frame_mapping)} 🚨🚨🚨")
        for key, cards in self.previous_frame_mapping.items():
            region_id, label = key
            print(f"  映射: 区域{region_id}-'{label}' -> {len(cards)}张卡牌")
            if region_id == 4:
                print(f"    🚨🚨🚨 [REGION_4_MAPPING] 发现区域4前一帧数据！🚨🚨🚨")
                for card in cards:
                    twin_id = card.get('twin_id', 'None')
                    print(f"      区域4前一帧卡牌: ID='{twin_id}'")

        # 优先级1: 本区域状态继承（4区域 → 4区域）
        lookup_key_4 = (4, original_label)
        print(f"🚨🚨🚨 [PRIORITY_1] 检查优先级1: 查找键{lookup_key_4} 🚨🚨🚨")
        print(f"🚨🚨🚨 [PRIORITY_1] 键是否存在: {lookup_key_4 in self.previous_frame_mapping} 🚨🚨🚨")

        if lookup_key_4 in self.previous_frame_mapping:
            print(f"🚨🚨🚨 [PRIORITY_1_SUCCESS] 优先级1成功: 从4区域前一帧继承 🚨🚨🚨")
            logger.info(f"🔧 优先级1: 从4区域前一帧继承")
            previous_cards_4 = self.previous_frame_mapping[lookup_key_4]
            print(f"🚨🚨🚨 [PRIORITY_1_CARDS] 前一帧4区域卡牌数: {len(previous_cards_4)} 🚨🚨🚨")

            # 使用空间排序进行匹配
            print(f"🚨🚨🚨 [SPATIAL_MATCH] 开始空间匹配: 当前{len(current_cards_list)}张 vs 前一帧{len(previous_cards_4)}张 🚨🚨🚨")
            matched_pairs = self._match_cards_by_spatial_order(current_cards_list, previous_cards_4, 4)
            print(f"🚨🚨🚨 [SPATIAL_RESULT] 空间匹配结果: {len(matched_pairs)}对匹配 🚨🚨🚨")

            # 处理匹配的卡牌
            for i, (current_card, previous_card) in enumerate(matched_pairs):
                current_label = current_card.get('label', 'None')
                previous_twin_id = previous_card.get('twin_id', 'None')
                print(f"🚨🚨🚨 [MATCH_PAIR_{i}] 匹配对{i}: 当前'{current_label}' <- 前一帧ID'{previous_twin_id}' 🚨🚨🚨")
                inherited_card = self._inherit_card_content_replacement(current_card, previous_card)
                inherited_cards.append(inherited_card)
                self.inheritance_stats["total_inherited"] += 1

                twin_id = inherited_card.get('twin_id', 'None')
                print(f"🚨🚨🚨 [INHERIT_SUCCESS] 继承成功: 当前'{current_label}' -> ID'{twin_id}' 🚨🚨🚨")
                logger.info(f"🔧 区域4本区域继承成功: ID='{twin_id}'")

            # 处理未匹配的当前帧卡牌
            matched_current_cards = {id(pair[0]) for pair in matched_pairs}
            unmatched_current_cards = [card for card in current_cards_list if id(card) not in matched_current_cards]

            print(f"🚨🚨🚨 [UNMATCHED] 未匹配的当前帧卡牌: {len(unmatched_current_cards)}张 🚨🚨🚨")
            for card in unmatched_current_cards:
                label = card.get('label', 'None')
                print(f"  未匹配卡牌: 标签'{label}'")

            if unmatched_current_cards:
                print(f"🚨🚨🚨 [CROSS_REGION] 区域4本区域继承: {len(unmatched_current_cards)}张卡牌无法匹配，尝试跨区域继承 🚨🚨🚨")
                logger.info(f"🔧 区域4本区域继承: {len(unmatched_current_cards)}张卡牌无法匹配，尝试跨区域继承")
                # 对未匹配的卡牌尝试跨区域继承
                return self._try_region_4_cross_region_inheritance(unmatched_current_cards, original_label, inherited_cards, new_cards)

            print(f"🚨🚨🚨 [PRIORITY_1_COMPLETE] 优先级1处理完成，所有卡牌都匹配成功 🚨🚨🚨")
            return True

        # 优先级2和3: 跨区域继承
        print(f"🚨🚨🚨 [PRIORITY_2_3] 区域4前一帧无数据，尝试跨区域继承 🚨🚨🚨")
        logger.info(f"🔧 区域4前一帧无数据，尝试跨区域继承")
        return self._try_region_4_cross_region_inheritance(current_cards_list, original_label, inherited_cards, new_cards)

    def _try_region_4_cross_region_inheritance(self, current_cards_list: List[Dict[str, Any]],
                                             original_label: str,
                                             inherited_cards: List[Dict[str, Any]],
                                             new_cards: List[Dict[str, Any]]) -> bool:
        """
        区域4跨区域继承：按优先级2区域 → 1区域
        """
        base_label = self._extract_original_label(original_label)

        # 优先级2: 从2区域继承（2区域 → 4区域）
        lookup_key_2 = (2, original_label)
        if lookup_key_2 in self.previous_frame_mapping:
            logger.info(f"🔧 优先级2: 从2区域继承")
            previous_cards_2 = self.previous_frame_mapping[lookup_key_2]

            # 选择最新的卡牌（ID最大的）
            latest_card_2 = self._find_latest_card(previous_cards_2)
            if latest_card_2:
                # 为当前4区域的所有卡牌继承选中的ID
                for current_card in current_cards_list:
                    inherited_card = self._inherit_card_content_replacement(current_card, latest_card_2)
                    inherited_cards.append(inherited_card)
                    self.inheritance_stats["total_inherited"] += 1

                    twin_id = inherited_card.get('twin_id', 'None')
                    logger.info(f"🔧 区域4从2区域继承成功: ID='{twin_id}'")

                return True

        # 优先级3: 从1区域继承（1区域 → 4区域）
        # 查找1区域中相同基础标签的所有卡牌
        region_1_matches = []
        for lookup_key, prev_cards_list in self.previous_frame_mapping.items():
            prev_group_id, prev_label = lookup_key
            if prev_group_id == 1:  # 只查找区域1
                prev_base_label = self._extract_original_label(prev_label)
                if prev_base_label == base_label:
                    region_1_matches.extend(prev_cards_list)
                    logger.info(f"🔧 1区域找到匹配: '{prev_label}' -> '{prev_base_label}', 卡牌数{len(prev_cards_list)}")

        if region_1_matches:
            logger.info(f"🔧 优先级3: 从1区域继承，找到{len(region_1_matches)}张匹配卡牌")

            # 🔧 选择ID最大的卡牌（如1二、2二、3二 → 选3二）
            latest_card_1 = self._find_latest_card(region_1_matches)
            if latest_card_1:
                # 为当前4区域的所有卡牌继承选中的ID
                for current_card in current_cards_list:
                    inherited_card = self._inherit_card_content_replacement(current_card, latest_card_1)
                    inherited_cards.append(inherited_card)
                    self.inheritance_stats["total_inherited"] += 1

                    twin_id = inherited_card.get('twin_id', 'None')
                    logger.info(f"🔧 区域4从1区域继承成功: ID='{twin_id}'（选择最大ID）")

                return True

        # 无法继承，作为新卡牌
        logger.info(f"🔧 区域4无法继承，{len(current_cards_list)}张卡牌作为新卡牌")
        new_cards.extend(current_cards_list)
        self.inheritance_stats["total_new"] += len(current_cards_list)
        return True

    def _process_region_6_priority_inheritance(self, current_cards_list: List[Dict[str, Any]],
                                             original_label: str,
                                             inherited_cards: List[Dict[str, Any]],
                                             new_cards: List[Dict[str, Any]]) -> bool:
        """区域6优先级继承处理"""
        logger.info(f"🔧 区域6优先级继承: 标签'{original_label}', {len(current_cards_list)}张卡牌")
        """
        🔧 区域6优先级继承逻辑：本区域优先策略

        优先级顺序：
        1. 优先级1: 本区域状态继承（6区域 → 6区域）
        2. 优先级2: 如果本区域无法完全满足，标记为新卡牌（避免错误的跨区域继承）

        Args:
            current_cards_list: 当前6区域的卡牌列表
            original_label: 原始标签
            inherited_cards: 继承卡牌列表（输出）
            new_cards: 新卡牌列表（输出）

        Returns:
            bool: 是否成功处理了6区域继承
        """
        logger.info(f"🔧 开始区域6优先级继承: 标签'{original_label}', 卡牌数{len(current_cards_list)}")

        # 优先级1: 本区域状态继承（6区域 → 6区域）
        lookup_key_6 = (6, original_label)

        if lookup_key_6 in self.previous_frame_mapping:
            previous_cards_6 = self.previous_frame_mapping[lookup_key_6]

            logger.info(f"🔧 优先级1: 从6区域前一帧继承，前一帧{len(previous_cards_6)}张")

            # 🔧 使用增强的本区域匹配算法（更宽松的容差）
            matched_pairs = self._match_cards_by_enhanced_region_6_algorithm(current_cards_list, previous_cards_6)

            # 处理匹配的卡牌
            for current_card, previous_card in matched_pairs:
                inherited_card = self._inherit_card_content_replacement(current_card, previous_card)
                # 🔧 为6区域本区域继承的卡牌添加保护标记，防止被跨区域继承
                inherited_card['region_6_protected'] = True
                inherited_card['region_6_local_inherited'] = True
                inherited_cards.append(inherited_card)
                self.inheritance_stats["total_inherited"] += 1

                twin_id = inherited_card.get('twin_id', 'None')
                logger.info(f"🔧 区域6本区域继承成功: ID='{twin_id}' (已保护)")

            # 处理未匹配的卡牌 - 标记为新卡牌（避免错误的跨区域继承）
            matched_current_cards = {id(pair[0]) for pair in matched_pairs}
            unmatched_current_cards = [card for card in current_cards_list if id(card) not in matched_current_cards]

            if unmatched_current_cards:
                logger.info(f"🔧 区域6有{len(unmatched_current_cards)}张无法本区域继承的卡牌，标记为新卡牌")
                new_cards.extend(unmatched_current_cards)
                self.inheritance_stats["total_new"] += len(unmatched_current_cards)

            return True

        # 🔧 优先级1.5: 基础标签匹配（精确匹配失败时的补充策略）
        logger.info(f"🔧 区域6精确匹配失败，尝试基础标签匹配")
        base_label = self._extract_base_label(original_label)
        region_6_base_matches = self._find_region_cards_by_base_label(6, base_label)

        if region_6_base_matches:
            logger.info(f"🔧 区域6基础标签匹配成功: 基础标签'{base_label}', 找到{len(region_6_base_matches)}张匹配卡牌")

            # 选择ID最大的卡牌进行继承
            latest_card = self._find_latest_card(region_6_base_matches)
            if latest_card:
                # 为当前6区域的所有卡牌继承选中的ID
                for current_card in current_cards_list:
                    inherited_card = self._inherit_card_content_replacement(current_card, latest_card)
                    # 🔧 为6区域基础标签继承的卡牌添加保护标记
                    inherited_card['region_6_protected'] = True
                    inherited_card['region_6_base_label_inherited'] = True
                    inherited_card['region_6_priority_inherited'] = True  # 🔧 新增：标记为优先级继承
                    inherited_card['base_label_source'] = latest_card.get('twin_id')
                    inherited_cards.append(inherited_card)
                    self.inheritance_stats["total_inherited"] += 1

                    twin_id = inherited_card.get('twin_id', 'None')
                    source_id = latest_card.get('twin_id', 'None')
                    logger.info(f"🔧 区域6基础标签继承成功: '{original_label}' -> ID='{twin_id}' (来源: '{source_id}')")

                return True

        # 没有前一帧数据，全部作为新卡牌
        logger.info(f"🔧 区域6无前一帧数据，{len(current_cards_list)}张卡牌作为新卡牌")
        new_cards.extend(current_cards_list)
        self.inheritance_stats["total_new"] += len(current_cards_list)
        return True

    def _match_cards_by_enhanced_region_6_algorithm(self, current_cards: List[Dict[str, Any]],
                                                   previous_cards: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any],
                                                                                                      Dict[str, Any]]]:
        """
        🔧 区域6增强匹配算法：专门处理位置偏移的本区域继承

        核心策略：
        1. 强制一对一匹配，确保所有卡牌都能继承
        2. 使用空间排序 + 标签匹配的混合策略
        3. 优先保证本区域继承，避免跨区域继承

        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表

        Returns:
            List[Tuple]: [(当前帧卡牌, 前一帧卡牌)] 匹配对列表
        """
        logger.info(f"🔧 区域6增强匹配: 当前{len(current_cards)}张, 前一帧{len(previous_cards)}张")

        # 🔧 强制匹配策略：确保所有当前帧卡牌都能匹配到前一帧卡牌
        if len(current_cards) <= len(previous_cards):
            # 当前帧卡牌数 <= 前一帧卡牌数，使用空间排序匹配
            logger.info(f"🔧 当前帧卡牌数({len(current_cards)}) <= 前一帧卡牌数({len(previous_cards)})，使用空间排序匹配")

            # 对两帧卡牌都进行相同的空间排序
            current_sorted = self._sort_cards_by_region_6_rule(current_cards)
            previous_sorted = self._sort_cards_by_region_6_rule(previous_cards)

            # 一对一匹配（取前N张）
            matched_pairs = []
            for i in range(len(current_sorted)):
                matched_pairs.append((current_sorted[i], previous_sorted[i]))

            logger.info(f"🔧 空间排序匹配完成: {len(matched_pairs)}对")
            return matched_pairs
        else:
            # 当前帧卡牌数 > 前一帧卡牌数，使用标签优先匹配
            logger.info(f"🔧 当前帧卡牌数({len(current_cards)}) > 前一帧卡牌数({len(previous_cards)})，使用标签优先匹配")
            return self._match_cards_by_label_priority(current_cards, previous_cards)

    def _sort_cards_by_region_6_rule(self, cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        🔧 区域6专用排序规则：从下到上，从左到右

        Args:
            cards: 卡牌列表

        Returns:
            List[Dict]: 排序后的卡牌列表
        """
        def get_position(card):
            points = card.get('points', [[0, 0]])
            if points and len(points) > 0:
                return points[0][0], points[0][1]  # x, y
            return 0, 0

        # 按Y坐标从大到小（从下到上），然后按X坐标从小到大（从左到右）
        sorted_cards = sorted(cards, key=lambda card: (-get_position(card)[1], get_position(card)[0]))

        logger.debug(f"区域6排序: {len(cards)}张卡牌按从下到上、从左到右排序")
        return sorted_cards

    def _match_cards_by_label_priority(self, current_cards: List[Dict[str, Any]],
                                     previous_cards: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """
        🔧 基于标签优先的匹配算法

        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表

        Returns:
            List[Tuple]: 匹配对列表
        """
        matched_pairs = []
        used_previous_indices = set()

        # 为每张当前卡牌寻找最佳匹配
        for current_card in current_cards:
            current_label = current_card.get('label', '')
            best_match = None
            best_match_index = -1

            # 寻找相同标签的前一帧卡牌
            for i, previous_card in enumerate(previous_cards):
                if i in used_previous_indices:
                    continue

                previous_label = previous_card.get('label', '')
                if current_label == previous_label:
                    best_match = previous_card
                    best_match_index = i
                    break

            if best_match:
                matched_pairs.append((current_card, best_match))
                used_previous_indices.add(best_match_index)
                logger.debug(f"标签匹配: '{current_label}' 匹配成功")

        logger.info(f"🔧 标签优先匹配完成: {len(matched_pairs)}对")
        return matched_pairs

def create_simple_inheritor():
    """创建基于区域状态的继承器"""
    return SimpleInheritor()
