#!/usr/bin/env python3
"""
检查Frame_00123和Frame_00124的区域6数据
"""

import json
from pathlib import Path

def check_frames():
    """检查Frame_00123和Frame_00124"""
    print("🔍 检查Frame_00123和Frame_00124的区域6数据")
    print("=" * 80)
    
    # 检查Frame_00123
    frame_123_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00123.json")
    if frame_123_file.exists():
        with open(frame_123_file, 'r', encoding='utf-8') as f:
            frame_123_data = json.load(f)
        
        region_6_123 = [s for s in frame_123_data.get('shapes', []) if s.get('group_id') == 6]
        print(f"📊 Frame_00123区域6: {len(region_6_123)}张卡牌")
        for i, shape in enumerate(region_6_123):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"  卡牌{i+1}: {label} → {twin_id} 位置=({x:.1f}, {y:.1f})")
    else:
        print("❌ Frame_00123输出文件不存在")
        return
    
    print()
    
    # 检查Frame_00124
    frame_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    if frame_124_file.exists():
        with open(frame_124_file, 'r', encoding='utf-8') as f:
            frame_124_data = json.load(f)
        
        region_6_124 = [s for s in frame_124_data.get('shapes', []) if s.get('group_id') == 6]
        print(f"📊 Frame_00124区域6: {len(region_6_124)}张卡牌")
        for i, shape in enumerate(region_6_124):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
            points = shape.get('points', [])
            x, y = points[0] if points else (0, 0)
            print(f"  卡牌{i+1}: {label} → {twin_id} 位置=({x:.1f}, {y:.1f})")
    else:
        print("❌ Frame_00124输出文件不存在")
        return
    
    print()
    
    # 分析继承情况
    print("🔍 继承分析:")
    print("=" * 80)
    
    # 查找"八"的继承情况
    ba_123 = [s for s in region_6_123 if s.get('label', '').endswith('八')]
    ba_124 = [s for s in region_6_124 if s.get('label', '').endswith('八')]
    
    print(f"📊 '八'的继承情况:")
    print(f"  Frame_00123: {len(ba_123)}张")
    for shape in ba_123:
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
        print(f"    {label} → {twin_id}")
    
    print(f"  Frame_00124: {len(ba_124)}张")
    for shape in ba_124:
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
        print(f"    {label} → {twin_id}")
    
    # 检查是否正确继承
    if ba_123 and ba_124:
        expected_id = ba_123[0].get('attributes', {}).get('digital_twin_id', 'N/A')
        actual_id = ba_124[0].get('attributes', {}).get('digital_twin_id', 'N/A')
        
        if expected_id == actual_id:
            print(f"  ✅ 继承正确: {expected_id}")
        else:
            print(f"  ❌ 继承错误: 期望{expected_id}, 实际{actual_id}")
    
    # 检查重复ID
    print(f"\n🔍 重复ID检查:")
    print("=" * 80)
    
    all_ids_124 = []
    for shape in frame_124_data.get('shapes', []):
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        if twin_id:
            all_ids_124.append(twin_id)
    
    duplicate_ids = []
    seen_ids = set()
    for twin_id in all_ids_124:
        if twin_id in seen_ids:
            duplicate_ids.append(twin_id)
        else:
            seen_ids.add(twin_id)
    
    if duplicate_ids:
        print(f"❌ 发现重复ID: {duplicate_ids}")
        
        # 查找重复ID的详细信息
        for dup_id in duplicate_ids:
            shapes_with_id = []
            for shape in frame_124_data.get('shapes', []):
                if shape.get('attributes', {}).get('digital_twin_id') == dup_id:
                    shapes_with_id.append(shape)
            
            print(f"  重复ID '{dup_id}' 出现在:")
            for shape in shapes_with_id:
                label = shape.get('label', '')
                group_id = shape.get('group_id')
                points = shape.get('points', [])
                x, y = points[0] if points else (0, 0)
                print(f"    区域{group_id}: {label} 位置=({x:.1f}, {y:.1f})")
    else:
        print(f"✅ 没有重复ID")

if __name__ == "__main__":
    check_frames()
