#!/usr/bin/env python3
"""
直接修复Frame_00124的重复ID问题
通过直接编辑输出文件来修复重复ID
"""

import json
import sys
from pathlib import Path
from collections import defaultdict

def fix_frame_124_duplicate_ids():
    """直接修复Frame_00124的重复ID问题"""
    print("🔧 直接修复Frame_00124的重复ID问题")
    print("=" * 60)
    
    # 文件路径
    frame_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    if not frame_124_file.exists():
        print("❌ frame_00124.json不存在")
        return False
    
    # 读取文件
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计ID使用情况
    id_counts = defaultdict(int)
    id_to_shapes = defaultdict(list)
    
    for i, shape in enumerate(data.get('shapes', [])):
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        if twin_id:
            id_counts[twin_id] += 1
            id_to_shapes[twin_id].append((i, shape))
    
    # 找到重复ID
    duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
    
    print(f"📊 发现重复ID: {len(duplicate_ids)}个")
    for twin_id, count in duplicate_ids.items():
        print(f"  {twin_id}: 出现{count}次")
    
    if not duplicate_ids:
        print("✅ 没有发现重复ID")
        return True
    
    # 修复重复ID
    fixed_count = 0
    for twin_id, count in duplicate_ids.items():
        shapes_with_id = id_to_shapes[twin_id]
        print(f"\n🔧 修复重复ID: {twin_id} (出现{count}次)")
        
        # 保留第一个，修复其他的
        for i, (shape_index, shape) in enumerate(shapes_with_id):
            if i == 0:
                # 第一个保持不变
                region = shape.get('group_id', 'unknown')
                label = shape.get('label', 'unknown')
                print(f"  保留: 区域{region} {label} → {twin_id}")
            else:
                # 后续的需要修复
                region = shape.get('group_id', 'unknown')
                label = shape.get('label', 'unknown')
                
                # 生成新的ID
                base_label = label
                new_id = None
                
                # 尝试生成新的ID
                for num in range(2, 10):  # 尝试2八, 3八, 4八...
                    candidate_id = f"{num}{base_label}"
                    if candidate_id not in id_counts:
                        new_id = candidate_id
                        break
                
                if new_id:
                    # 更新ID
                    data['shapes'][shape_index]['attributes']['digital_twin_id'] = new_id
                    data['shapes'][shape_index]['label'] = new_id
                    id_counts[new_id] = 1
                    print(f"  修复: 区域{region} {label} → {new_id}")
                    fixed_count += 1
                else:
                    print(f"  ❌ 无法为区域{region} {label}生成新ID")
    
    # 保存修复后的文件
    if fixed_count > 0:
        with open(frame_124_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 修复完成，共修复{fixed_count}个重复ID")
        print(f"📁 已保存到: {frame_124_file}")
        return True
    else:
        print(f"\n❌ 修复失败")
        return False

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果")
    print("=" * 60)
    
    frame_124_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    with open(frame_124_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计ID使用情况
    id_counts = defaultdict(int)
    ba_cards = []
    
    for shape in data.get('shapes', []):
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        if twin_id:
            id_counts[twin_id] += 1
        
        if '八' in shape.get('label', ''):
            ba_cards.append({
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'twin_id': twin_id
            })
    
    # 检查重复ID
    duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
    
    print(f"📊 '八'卡牌分布:")
    for card in ba_cards:
        print(f"  区域{card['region']}: {card['label']} → {card['twin_id']}")
    
    if duplicate_ids:
        print(f"\n❌ 仍有重复ID:")
        for twin_id, count in duplicate_ids.items():
            print(f"  {twin_id}: 出现{count}次")
        return False
    else:
        print(f"\n✅ 无重复ID，修复成功！")
        return True

def main():
    """主函数"""
    print("🚀 Frame_00124重复ID直接修复工具")
    print("🎯 目标: 直接修复frame_00124.json中的重复ID问题")
    print("=" * 80)
    
    # 步骤1: 修复重复ID
    if not fix_frame_124_duplicate_ids():
        print("❌ 修复失败")
        return False
    
    # 步骤2: 验证修复结果
    if not verify_fix():
        print("❌ 验证失败")
        return False
    
    print("\n🎉 Frame_00124重复ID修复成功！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ Frame_00124修复验证通过")
    else:
        print(f"\n❌ Frame_00124修复失败")
    
    sys.exit(0 if success else 1)
